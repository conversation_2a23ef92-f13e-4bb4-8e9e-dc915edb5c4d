# 供应商对账
- 对于供应商对账明细Excel导出，用户偏好基于平铺列表结构导出，而不是按订单分组。
- 用户偏好将复杂对象赋值逻辑提取到单独方法中，特别是将requestData赋值和requestDtl赋值分离到不同方法中以便更好地组织代码。
- 用户偏好将通用InterfaceLogs初始化模式提取到单独方法中以便更好地组织代码并避免重复。用户偏好简单的单方法解决方案进行代码重构，而不是在提取InterfaceLogs创建等通用模式时使用复杂的多方法方式。
- 用户偏好将通用InterfaceLogs初始化模式提取到单独的辅助方法中以便更好地组织代码。
- 用户偏好将通用InterfaceLogs初始化模式提取到单独方法中以便更好地组织代码，特别是要求对MaterialReconciliationServiceImpl类采用与MaterialReconciliationController相同的重构方法。
- 对于供应商对账审核通过（情况3），遵循MaterialReconciliationServiceImpl.materialReconciliationAuditPlan模式，在pushToPCWP调用后，添加platformDealFeeDtlService.checkDealAmount来处理支付交易费用。
- 对于平台费用检查，偏好将SupplierReconciliation转换为MaterialReconciliation并调用现有的checkDealAmount(MaterialReconciliation mr, int operateType)方法，而不是创建单独的重载方法。
- 对于供应商对账取消优化，参考materialReconciliationCancellation模式以在对账取消功能中保持一致的实现方法。
- 对于PCWP取消方法如cancelPCWPBill，参考MaterialReconciliationServiceImpl.materialReconciliationCancellation方法中使用的实现模式进行优化，因为它具有相同的逻辑模式但针对不同表，具有相同的PCWP接口调用，并添加checkOutBillCanBeInvalidated方法验证以检查外部单据是否可以取消。
- 对于对账操作，添加用户类型验证 - 如果用户是内部用户，调用PCWP集成；否则，直接修改物料商城数据而不调用PCWP。
- 对于计划对账优化，参考OrderShipDtlController.getReconcilableMaterialList方法模式，使用SQL查询连接orders、order_ship_dtl、order_ship表，按type=2（已收料）、product_type=13（大宗临采）过滤，返回billNo/orderSn/orgName/gmtCreate字段供前端显示。
- 对于计划对账企业查询，使用SQL模式连接orders/order_ship_dtl/order_ship/enterprise_info表，按type=2（已收料）、product_type=13（大宗临采）过滤，LEFT JOIN material_reconciliation_dtl获取已对账数量，按enterprise_name分组，使用MAX(DATE(gmt_create))获取最新时间，按gmt_create2 DESC排序。
- 对于materialReconciliationSTCancellation方法，仅在isNotPush==1条件满足时调用pcwpService。
- 对于PCWP推送状态确定，检查订单是否关联计划，然后检查计划的p_bill_no是否存在 - 如果存在，计划已推送到PCWP（isNotPush=1），否则未推送（isNotPush=0）。
- 用户偏好将PCWP推送状态确定逻辑（检查订单是否关联计划以及计划的p_bill_no是否存在）提取到单独的辅助方法中以便更好地组织代码。
- 对于materialReconciliationSTDelete方法优化，重构直接的restTemplateUtils调用以使用pcwpService方法，遵循已建立的PCWP集成模式。
- 对于计划对账服务，使用清晰的命名约定：供应商端方法应有'BySupplier'后缀（getReconciliablePlansBySupplierPageList、getReconciliableEnterpriseBySupplierPageList），企业端方法应有'ByEnterprise'后缀（getReconciliableSupplierByEnterprisePageList、getReconciliablePlansByEnterprisePageList）以便更好地区分。

# 查询参数
- 对于查询中的productType参数，前端传递逗号分隔的值如'1,10'、'2,13'或单个值如'3'。
- 对于查询中的order_sn参数，支持以逗号分隔字符串格式传递的多个值。
- 对于大宗临采可对账物料查询，使用订单类型13（o.product_type = 13）并避免product_material表连接，而是直接使用order_ship_dtl字段与product_id匹配进行对账计算。
- 对于getValidSupplierList查询，使用SQL模式连接order_select_plan/orders/order_ship_dtl/order_ship表，按product_type、is_reconciliation=0（未对账）、type=2（已收料）、ship_num>0、receive_time IS NOT NULL过滤，可选过滤shortCode/keywords/orgId/enterpriseId。

# PCWP集成
- 对于PCWP pushToPCWP方法，使用详细的JSON格式，包含data对象，其中包含acceptanceAmount、acceptanceDate、billNo、orgId、supplierId、supplierName、dtls数组（包含materialId/materialName/acceptanceQuantity/materialNum/materialPrice/materialAmount字段）和用于操作标识的keyId。
- PCWP API期望日期格式为'yyyy-MM-dd'，但接收'yyyy/MM/dd'格式时会失败，导致JSON解析错误。
- 对于PCWP集成，在PcwpThirdApiClient类中添加isCanOperaBill方法以检查是否可以执行对账，使用带有orgId和date参数的模式。
- 对于PCWP集成，在PcwpThirdApiClient类中添加方法以检查是否可以进行对账（用于物料采购平台），使用带有orgId和date参数的模式。
- 对于PCWP服务方法如saveAcceptance和clearRelationId，遵循与isCanOperaBill方法相同的实现模式。
- 对于PCWP服务方法如saveAcceptance，偏好传递专用的PCWP对象作为参数，而不是使用Map<String, Object>进行参数传递。
- 对于PCWP saveAcceptance方法调用，偏好直接传递SupplierReconciliation对象，而不是使用Map进行参数传递。
- 对于PCWP集成，重构直接的restTemplateUtils调用（postPCWP2用于SAVE_ACCEPTANCE_URL和getPCWP2NotParams用于CLEAR_RELATION_ID_URL）以使用PcwpService方法，遵循为isCanOperaBill方法建立的模式。
- 对于PCWP集成，重构WRITE_BACK_BILL_LOCK_QUANTITY_URL的直接restTemplateUtils调用以使用PcwpService.writeBackBillLockQuantiy方法，遵循其他PCWP操作的既定模式。
- 对于PcwpAcceptanceRequest，JSON格式包含data对象，其中包含acceptanceAmount、acceptanceDate、billNo、orgId、supplierId、supplierName、dtls数组（包含materialId/materialName/acceptanceQuantity/materialNum/materialPrice/materialAmount字段）和用于操作标识的keyId。PcwpAcceptanceRequest应包含data对象中的sourceBillName等附加字段，dtls应包含dtlId、factoryPrice、fixedFee、freight、materialClassId、materialClassName、networkPrice、notAcceptedQuantity、orderId、orderNo、sourceDtlId、taxAmount、texture、tradeId等字段以完全兼容PCWP。
- 对于PcwpSiteReceiptRequest，JSON格式包含data数组，其中包含amount（数字）、dtlId（字符串）、quantity（数字），以及根级别的keyId和orgId字符串。
- 对于PCWP API客户端方法，始终使用方法定义和注释中指定的确切返回数据类型，确保代码优化精确遵循API规范。
- 对于PCWP集成，偏好在业务方法中直接调用PcwpService类，而不是使用callPcwpApi等包装方法以获得更清洁的代码结构。

# 数据去重
- 用户偏好基于SQL的去重解决方案而不是业务层处理，认为业务层方法繁琐。
- 对于具有排序偏好的SQL去重，使用窗口函数模式：ROW_NUMBER() OVER (PARTITION BY columns ORDER BY sort_column DESC)，配合WHERE rn = 1获取每组的最新记录。
- 对于带有窗口函数的复杂SQL，用户偏好从目标SQL结构一次性逆向工程MyBatis-Plus代码以避免语法错误，而不是迭代调试方法。

# MyBatis-Plus
- 对于MyBatis-Plus GROUP BY查询，当MySQL sql_mode包含ONLY_FULL_GROUP_BY时，所有SELECT列必须在GROUP BY子句中或使用聚合函数以避免SQLSyntaxErrorException。
- 对于映射器方法参数，偏好使用专用DTO对象如ReconcilableMaterialDTO而不是Map<String, Object>以获得更好的类型安全性和代码清晰度。
- 对于分页方法，当count等于0时，直接返回空结果而不执行实际查询以提高性能。

# 物料对账价格字段
- 对于物料对账价格字段，前端显示type=1（浮动价格）的freightPrice/fixationPrice和type=2（固定价格）的outFactoryPrice/transportPrice，这应该与从OrderItem属性设置这些字段的后端映射逻辑匹配。

# 代码优化偏好
- 用户偏好在方法内进行内联代码优化，而不是为重构任务提取单独的辅助方法。
- 用户偏好将审核记录创建模式（特别是resultType=2、auditType=1、relevanceType=5的拒绝）提取到单独的辅助方法中以便更好地组织代码。

# TT待办系统RabbitMQ集成
- 对于TT待办系统RabbitMQ集成：使用Vhost 'ToDoList'、Exchange 'TT_ToDoList'、routingKey 'Increment'进行实时更新，发送待办事项两次（status=0表示待处理，status=1表示已完成），消息格式包括message_id/message_system/message_date/message_body，具有特定字段要求，包括toDoId、employeeNumber、userId、webUrl（自动附加a/p参数）。
- 对于TT待办系统：RabbitMQ凭据（wzsc/FABF26F6-7615-EB92-E158-09F3125DC089），测试账户employeeNumber 036529/userId 391E2FB8-F295-4045-8CDC-340AD3DE6700，登录凭据036529/znjztest@036529，TT应用URL app.scrbg.com。

# 采购类型
- 对于StandardExampleUsage，添加productType参数，值为：0=零星采购（零售采购），1=大宗临购（批量采购），2=周转材料（周转材料）以确定调用哪种特定类型的逻辑。
- StandardExampleUsage处理不同的采购类型：零星采购（零星采购）、大宗临购（批量采购）和周转材料（周转材料），具有不同的参数配置。

# 错误消息
- 对于商店相关的错误消息，偏好在错误消息中包含商店名称以获得更好的用户体验，而不是仅仅使用通用消息。

# 订单创建响应
- 对于OrderCreateResultVO响应，orderId、orderSn和productType字段都应该有实际值而不是默认值，productType应该从实际订单数据确定而不是硬编码默认值。
