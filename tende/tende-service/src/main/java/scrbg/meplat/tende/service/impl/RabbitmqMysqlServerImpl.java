package scrbg.meplat.tende.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.tende.entity.*;
import scrbg.meplat.tende.service.*;
import scrbg.meplat.tende.vo.*;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class RabbitmqMysqlServerImpl  implements RabbitmqMysqlServer {
    @Autowired
    private TenderService tenderService;
    @Autowired
    private TenderDtlService tenderDtlService;
    @Autowired
    private TenderPackageService tenderPackageService;
    @Autowired
    private TenderPackageSubcontractorService tenderPackageSubcontractorService;
    @Autowired
    private TenderResultService tenderResultService;
    @Autowired
    private TenderResultCandidateService tenderResultCandidateService;
    @Autowired
    private ITenderPublicityService tenderPublicityService;
    @Autowired
    private ITenderPublicityCandidateService tenderPublicityCandidateService;
    @Autowired
    private ITenderPublicityComplaintService tenderPublicityComplaintService;
    @Autowired
    private TenderEnrollPackageService tenderEnrollPackageService;
    @Autowired
    private ITenderEnrollService tenderEnrollService;
    @Autowired
    private TenderNoticeContentService tenderNoticeContentService;
    @Autowired
    private TenderNoticeService tenderNoticeService;
    @Autowired
    private ITenderMessageService tenderMessageService;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void listenMallTenderQueue1(Map<String, Object> innerMap) {
        //判断方法
        String method = (String) innerMap.get("method");
        if (method!=null){
            if (method.equals("create")){
                JSONObject jsonStr = getCreateJsonSter(innerMap);
                TenderInfoVo tenderInfoVo = JSON.parseObject(String.valueOf(jsonStr), TenderInfoVo.class);
                saveTenderInfo(tenderInfoVo);
                }else {
                Object data = innerMap.get("data");
                String s = data.toString();
                ArrayList<TenderInfoVo> tenderInfoVos = new ArrayList<>();
                tenderInfoVos = (ArrayList<TenderInfoVo>) JSON.parseArray(s, TenderInfoVo.class);
                for (TenderInfoVo tenderInfoVo : tenderInfoVos) {
                    saveTenderInfo(tenderInfoVo);

                }
            }


            }
        }

    private void saveTenderInfo(TenderInfoVo tenderInfoVo) {
        Integer tendType=changBillType(tenderInfoVo.getBillType());
        if (tendType!=null){
            tenderInfoVo.setTenderType(tendType);
            if (tendType==1||tendType==2){

                tenderInfoVo.setIsDevice(1);
            }else {
                tenderInfoVo.setIsMaterial(1);
            }
        }

        saveTender(tenderInfoVo);
        List<TenderPackageInfoVo> tenderPackageList = tenderInfoVo.getTenderPackages();
        if (tenderPackageList!=null&&tenderPackageList.size()>0){
            saveTenderPackage(tenderPackageList);
        }

    }

    private void saveTenderPackage(List<TenderPackageInfoVo> tenderPackageList) {

//        List<String> collect = tenderPackageList.stream().map(TenderPackageInfoVo::getDtlId).collect(Collectors.toList());
        for (TenderPackageInfoVo tenderPackageInfoVo : tenderPackageList) {
            TenderPackage byId = tenderPackageService.getById(tenderPackageInfoVo.getDtlId());
            if (byId!=null){
                tenderPackageService.update(tenderPackageInfoVo);
            }else {
                tenderPackageService.save(tenderPackageInfoVo);
            }

        }
        for (TenderPackageInfoVo tenderPackageInfoVo : tenderPackageList) {
            List<TenderDtl> tenderDtls = tenderPackageInfoVo.getTenderDtls();
            if (tenderDtls!=null&&tenderDtls.size()>0){
                savetenderDtls(tenderDtls);
            }

            List<TenderPackageSubcontractor> tenderPackageSubcontractors = tenderPackageInfoVo.getTenderPackageSubcontractors();
            if (tenderPackageSubcontractors!=null&&tenderPackageSubcontractors.size()>0){
                saveTenderPackageSubcontractor(tenderPackageSubcontractors);
            }

        }
    }

    private void saveTenderPackageSubcontractor(List<TenderPackageSubcontractor> tenderPackageSubcontractors) {
        List<String> collect = tenderPackageSubcontractors.stream().map(TenderPackageSubcontractor::getRecordId).collect(Collectors.toList());
        tenderPackageSubcontractorService.removeByIds(collect);
        for (TenderPackageSubcontractor tenderPackageSubcontractor : tenderPackageSubcontractors) {
            tenderPackageSubcontractorService.save(tenderPackageSubcontractor);
        }
    }

    private void savetenderDtls(List<TenderDtl> tenderDtls) {
        List<String> collect = tenderDtls.stream().map(TenderDtl::getRecordId).collect(Collectors.toList());
        tenderDtlService.removeByIds(collect);
        for ( TenderDtl tenderDtl : tenderDtls) {
            tenderDtlService.save(tenderDtl);
        }
    }



    private void saveTender(TenderInfoVo tenderInfoVo) {
        Tender byId = tenderService.getById(tenderInfoVo.getBillId());
        if (byId!=null){
            tenderService.delete(byId.getBillId());
            tenderService.save(tenderInfoVo);
        }else {
            tenderService.save(tenderInfoVo);
        }
    }

    private Integer changBillType(Integer billType) {
        if (billType==60231){
            return 4;
        }
        if (billType==60275){
            return 5;
        }
        if (billType==60285){
            return 1;
        }
        if (billType==60295){
            return 2;
        }if (billType==60234){
            return 4;
        }
        if (billType==60274){
            return 5;
        }
        if (billType==60294){
            return 2;
        }
        if (billType==60284){
            return 1;
        }
        if (billType==6025){
            return 6;
        }
        if (billType==6027){
            return 5;
        }
        if (billType==6028){
            return 1;
        }
        if (billType==6029){
            return 2;
        }
        if (billType==60255){
            return 3;
        }
        if (billType==6023){
            return 4;
        }
        return  null;
    }

    @Override
    public void listenMallTenderResultQueue1(Map<String, Object> innerMap) {
        String method = (String) innerMap.get("method");
        if (method!=null){
            if (method.equals("create")){
                JSONObject jsonSter = getCreateJsonSter(innerMap);
                TenderResultInfoVo resultInfoVo = JSON.parseObject(String.valueOf(jsonSter), TenderResultInfoVo.class);
                saveTenderResultInfoVo(resultInfoVo);
            }else {
                String jsonSter = innerMap.get("list").toString();
                ArrayList<TenderResultInfoVo> tenderInfoVos = new ArrayList<>();
                tenderInfoVos = (ArrayList<TenderResultInfoVo>) JSON.parseArray(jsonSter, TenderResultInfoVo.class);
                for (TenderResultInfoVo tenderInfoVo : tenderInfoVos) {
                    saveTenderResultInfoVo(tenderInfoVo);
                }

            }
        }

    }

    private void saveTenderResultInfoVo(TenderResultInfoVo resultInfoVo) {
        if (resultInfoVo !=null){
            tenderResultService.saveOrUpdate(resultInfoVo);
            List<TenderResultCandidate> tenderResultCandidates = resultInfoVo.getTenderResultCandidates();

            if (tenderResultCandidates!=null&&tenderResultCandidates.size()>0){
                tenderResultCandidateService.saveOrUpdateBatch(tenderResultCandidates);
            }
        }
    }
    private void saveTenderPublicityInfoVo(TenderPublicityInfoVo tenderPublicityInfoVo) {
        if (tenderPublicityInfoVo !=null){
            tenderPublicityService.saveOrUpdate(tenderPublicityInfoVo);
            List<TenderPublicityCandidate> tenderPublicityCandidates = tenderPublicityInfoVo.getTenderPublicityCandidates();

            if (tenderPublicityCandidates!=null&&tenderPublicityCandidates.size()>0){
                tenderPublicityCandidateService.saveOrUpdateBatch(tenderPublicityCandidates);
            }
        }
    }

    private JSONObject getCreateJsonSter(Map<String, Object> innerMap) {
                String data =  innerMap.get("data").toString();
                JSONObject jsonStr = JSONObject.parseObject(data);
                return jsonStr;

    }


    @Override
    public void listenMallTenderPublicityQueue1(Map<String, Object> innerMap) {
        String method = (String) innerMap.get("method");
        if (method!=null){
            if (method.equals("create")){
                JSONObject jsonSter = getCreateJsonSter(innerMap);
                TenderPublicityInfoVo tenderPublicityInfoVo = JSON.parseObject(String.valueOf(jsonSter), TenderPublicityInfoVo.class);
                saveTenderPublicityInfoVo(tenderPublicityInfoVo);
            }else {
                String jsonSter = innerMap.get("list").toString();
                ArrayList<TenderPublicityInfoVo> tenderPublicityInfoVos = new ArrayList<>();
                tenderPublicityInfoVos = (ArrayList<TenderPublicityInfoVo>) JSON.parseArray(jsonSter, TenderPublicityInfoVo.class);
                for (TenderPublicityInfoVo tenderPublicityInfoVo : tenderPublicityInfoVos) {
                    saveTenderPublicityInfoVo(tenderPublicityInfoVo);
                }

            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void listenMallTenderChangQueue1(Map<String, Object> innerMap) {
        String method = (String) innerMap.get("method");
        if (method!=null){
            if (method.equals("create")){
                JSONObject jsonSter = getCreateJsonSter(innerMap);
                TenderInfoVo tenderInfoVo = JSON.parseObject(String.valueOf(jsonSter), TenderInfoVo.class);
                saveTenderInfo(tenderInfoVo);
            }else {
                String data = (String) innerMap.get("list");
                ArrayList<TenderInfoVo> tenderInfoVos = new ArrayList<>();
                tenderInfoVos = (ArrayList<TenderInfoVo>) JSON.parseArray(data, TenderInfoVo.class);
                for (TenderInfoVo tenderInfoVo : tenderInfoVos) {
                    saveTenderInfo(tenderInfoVo);
                }
            }
        }





    }

    @Override
    public void listenMallTenderEnroll(Map<String, Object> innerMap) {
        String method = (String) innerMap.get("method");
        if (method!=null){
            if (method.equals("create")){
                JSONObject jsonSter = getCreateJsonSter(innerMap);
                TenderEnrollInfoVO tenderEnrollInfoVO = JSON.parseObject(String.valueOf(jsonSter), TenderEnrollInfoVO.class);
               saveTenderEnrolls(tenderEnrollInfoVO);
            }else {
                String data = (String) innerMap.get("list");
                ArrayList<TenderEnrollInfoVO> tenderInfoVos = new ArrayList<>();
                tenderInfoVos = (ArrayList<TenderEnrollInfoVO>) JSON.parseArray(data, TenderEnrollInfoVO.class);
                for (TenderEnrollInfoVO tenderInfoVo : tenderInfoVos) {
                    saveTenderEnrolls(tenderInfoVo);
                }
            }
        }
    }

    private void saveTenderEnrolls(TenderEnrollInfoVO tenderEnrollInfoVO) {
        if (tenderEnrollInfoVO!=null){
            Tender byId = tenderService.getById(tenderEnrollInfoVO.getTenderApplyId());
            if (byId!=null){
                Integer isMaterial = byId.getIsMaterial();
                if (isMaterial==0){
                    tenderEnrollInfoVO.setMallConfig(1);
                }else {
                    tenderEnrollInfoVO.setMallConfig(1);
                }
            }
            tenderEnrollService.save(tenderEnrollInfoVO);
            List<TenderEnrollPackage> enrollPackages = tenderEnrollInfoVO.getEnrollPackages();
            if (enrollPackages!=null){
                for (TenderEnrollPackage enrollPackage : enrollPackages) {
                    tenderEnrollPackageService.save(enrollPackage);
                }

            }

        }
    }

    @Override
    public void listenMallTenderNotice(Map<String, Object> innerMap) {
        String method = (String) innerMap.get("method");
        if (method!=null){
            if (method.equals("create")){
                JSONObject jsonSter = getCreateJsonSter(innerMap);
                TenderNotice TenderNotice = JSON.parseObject(String.valueOf(jsonSter), TenderNotice.class);
            saveNoticeVo(TenderNotice);
            }else {
                String data = (String) innerMap.get("list");
                ArrayList<TenderNotice> tenderInfoVos = new ArrayList<>();
                tenderInfoVos = (ArrayList<TenderNotice>) JSON.parseArray(data, TenderNotice.class);
                for (TenderNotice tenderInfoVo : tenderInfoVos) {
                    saveNoticeVo(tenderInfoVo);
                }
            }
        }

    }

    private void saveNoticeVo(TenderNotice tenderNotice) {
        if (tenderNotice!=null){
         Tender tender=tenderService.getByBillNo(tenderNotice.getTenderApplyNo());
            JSONObject jsonObject = new JSONObject();
            Map<String, Object> innerMap = jsonObject.getInnerMap();
            innerMap.put("notice",tenderNotice.getBillId());
            innerMap.put("messageType",0);


         if (tender!=null){
             innerMap.put("keyword",tender.getBillId());
             if (tender.getIsMaterial()==1){
                 tenderNotice.setMallConfig(0);

             }else {
                 tenderNotice.setMallConfig(1);
             }
         }

            String tenderApplyId = tenderNotice.getTenderApplyId();
            String packageId = tenderNotice.getPackageId();
            TenderPackageSubcontractor info=  tenderPackageSubcontractorService.getUnitCode(tenderApplyId,packageId);
            if (info!=null) {
                tenderNotice.setUnitCode(info.getCreditCode());
            }
            tenderNoticeService.save(tenderNotice);
            TenderMessage msg= tenderMessageService.getData(jsonObject);
            if (msg==null){
                saveMessage(tenderNotice, tender, info);

            }



        }
    }

    private void saveMessage(TenderNotice tenderNotice, Tender tender, TenderPackageSubcontractor info) {
        TenderMessage tenderMessage = new TenderMessage();
        if (tender!=null){
            if (tender.getIsMaterial()==1){
                tenderMessage.setMallType(0);
            }else {
                tenderMessage.setMallType(0);
            }
        }
           if (info!=null){
               tenderMessage.setUnitCode(info.getCreditCode());
               tenderMessage.setKeyword(info.getBillId());
           }
        tenderMessage.setCount(tenderNotice.getContent());

        tenderMessage.setMessageType(0);
        tenderMessage.setIsRead(0);
        tenderMessage.setGmtCreate(new Date());
        tenderMessage.setSendName(tenderNotice.getNoticeUser());
        tenderMessage.setSendId(tenderNotice.getNoticeUserId());
        tenderMessage.setTitle("中标通知");
        tenderMessage.setSendTime(tenderNotice.getReleaseDate());
        tenderMessage.setNotice(tenderNotice.getBillId());
        tenderMessageService.save(tenderMessage);
    }
}
