package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.entity.InterfaceLogs;
import scrbg.meplat.mall.entity.OrderShip;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.InterfaceLogsService;
import scrbg.meplat.mall.service.OrderShipService;
import scrbg.meplat.mall.util.*;
import scrbg.meplat.mall.vo.app.ShipFiles;
import scrbg.meplat.mall.vo.ship.OrderShipVo;
import scrbg.meplat.mall.vo.ship.SubmitOrderShipDtl;
import scrbg.meplat.mall.vo.ship.SubmitOrderShipVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @描述：控制类
 * @作者: ye
 * @日期: 2023-05-22
 */
@RestController
@RequestMapping("/orderShip")
@Api(tags = "发货单")
public class OrderShipController {

    @Autowired
    private InterfaceLogsService interfaceLogsService;

    @Autowired
    public OrderShipService orderShipService;

    @Autowired
    MallConfig mallConfig;

    @Autowired
    private RestTemplateUtils restTemplateUtils;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<OrderShip> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderShipService.queryPage(jsonObject, new LambdaQueryWrapper<OrderShip>());
        return PageR.success(page);
    }

    @PostMapping("/WXlistByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<OrderShip> WXlistByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderShipService.WXqueryPage(jsonObject, new LambdaQueryWrapper<OrderShip>());
        return PageR.success(page);
    }

    @PostMapping("/listByTwoEntity")
    @ApiOperation(value = "零星采购发货单查看")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<OrderShip> listByTwoEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderShipService.queryPageTwo(jsonObject, new LambdaQueryWrapper<OrderShip>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<OrderShip> findById(String id) {
        OrderShip orderShip = orderShipService.getById(id);
        return R.success(orderShip);
    }

    @GetMapping("/purchaseOrderShipById")
    @ApiOperation(value = "采购员扫二维码接口")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "ID", required = true,
            dataType = "String", paramType = "query")
    })
    public R<OrderShip> purchaseOrderShipById(String id) {
        OrderShip orderShip = orderShipService.getById(id);
        if (orderShip == null) {
            throw new BusinessException(500, "发货单不存在，请联系管理员");
        }
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        if (orderShip.getEnterpriseId().equals(user.getEnterpriseId())) {
            return R.success(orderShip);
        }
        throw new BusinessException(500, "不是采购单位，无权限查看发货单");
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @NotResubmit
    public R save(@RequestBody OrderShip orderShip) {
        orderShipService.create(orderShip);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @NotResubmit
    public R update(@RequestBody OrderShip orderShip) {
        orderShipService.update(orderShip);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "供应商删除发货单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        orderShipService.delete(id);
        return R.success();
    }

    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        orderShipService.removeByIds(ids);
        return R.success();
    }

    @PostMapping("/createBatchByOrderItem")
    @ApiOperation(value = "根据订单项生成发货单")
    @NotResubmit
    public R createBatchByOrderItem(@RequestBody OrderShipVo orderShipVo) {
        orderShipService.saveOrderShip(orderShipVo);
        return R.success();
    }

    @PostMapping("/updateFiles")
    @ApiOperation(value = "（零星采购）修改配置文件")
    public R updateFiles(@RequestBody ShipFiles shipFiles) {
        orderShipService.updateFiles(shipFiles);
        return R.success();
    }

    @PostMapping("/changShipNum")
    @ApiOperation(value = "修改确认收货数量")
    @NotResubmit
    public R changShipNum(@RequestBody List<SubmitOrderShipDtl> dtls) {
        orderShipService.changShipNum(dtls);
        return R.success();
    }

    @PostMapping("/confirmShip")
    @NotResubmit
    @ApiOperation(value = "收料员确认发货单")
    // 确认收货啦
    public R confirmShip(@RequestBody SubmitOrderShipVo vo) {
        String idStr = IdWorker.getIdStr();
        idStr = vo.getBillId();//采用发货单id来保证收料永远也不翻倍
        StringBuilder stringBuilder = new StringBuilder();
        try {
            orderShipService.confirmShip(vo, idStr, stringBuilder);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "confirmShip", vo, null, null, e.getMessage(), OrderShipController.class);
            InterfaceLogs iLog = new InterfaceLogs();
            iLog.setSecretKey(idStr);
            iLog.setClassPackage(OrderShipController.class.getName());
            iLog.setMethodName("confirmShip");
            iLog.setLocalArguments(JSON.toJSONString(vo));
            iLog.setFarArguments(stringBuilder.toString());
            iLog.setIsSuccess(0);
            iLog.setLogType(1);
            iLog.setErrorInfo(e.getMessage());
            interfaceLogsService.create(iLog);
            InterfaceLogs rollBackiLog = new InterfaceLogs();
            rollBackiLog.setSecretKey(idStr);
            rollBackiLog.setClassPackage(OrderShipController.class.getName());
            rollBackiLog.setMethodName("confirmShip(rollback)");
            rollBackiLog.setLocalArguments(JSON.toJSONString(vo));
            rollBackiLog.setFarArguments(stringBuilder.toString());
            rollBackiLog.setLogType(2);
            if(!"收料推送重复".equals(e.getMessage())){
                try {
                    // 回滚
                    restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.ROLLBACK_SAVE_SITE_RECEIVING + idStr);
                    rollBackiLog.setIsSuccess(1);
                    interfaceLogsService.create(rollBackiLog);
                } catch (Exception ex) {
                    rollBackiLog.setIsSuccess(0);
                    rollBackiLog.setErrorInfo(e.getMessage());
                    interfaceLogsService.create(rollBackiLog);
                    ex.printStackTrace();
                }
                throw new BusinessException(e.getMessage());
            }
        }
        return R.success();
    }

    @PostMapping("/selectShipList")
    @ApiOperation(value = "收料员查询发货单")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "billSn", value = "发货单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "orderSn", value = "订单编号", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierName", value = "供应商企业", dataTypeClass = String.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<OrderShip> selectShipList(@RequestBody JSONObject jsonObject) {
        PageUtils page = orderShipService.selectShipList(jsonObject, new LambdaQueryWrapper<OrderShip>());
        return PageR.success(page);
    }

    @PostMapping("/updateShipType")
    @NotResubmit
    @ApiOperation(value = "供应商发货")
    @DynamicParameters(name = "", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public R updateShipType(@RequestBody OrderShip orderShip) {
        orderShipService.updateShipType(orderShip);
        return R.success();
    }

    @GetMapping("/exportDataById")
    @ApiOperation(value = "根据发货单Id导出发货单项数据")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "发货单Id", required = true,
            dataType = "String", paramType = "query")
    })
    public R exportDataByBillId(String id, HttpServletResponse response) {
        orderShipService.exportDataByBillId(id, response);
        return R.success();
    }

    @GetMapping("/exportDataTwoById")
    @ApiOperation(value = "根据发货单Id导出发货单项数据(二级订单)")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "发货单Id", required = true,
            dataType = "String", paramType = "query")
    })
    public R exportDataTwoById(String id, HttpServletResponse response) {
        orderShipService.exportDataTwoById(id, response);
        return R.success();
    }

    @GetMapping("/materialShipExport")
    @ApiOperation(value = "物资零星收料单模板导出")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "发货单Id", required = true,
            dataType = "String", paramType = "query")
    })
    public R materialShipExport(String id, HttpServletResponse response) {
        orderShipService.materialShipExport(id, response);
        return R.success();
    }

    @GetMapping("/materialShipDzExport")
    @ApiOperation(value = "物资收料单模板导出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "发货单Id", required = true,
                    dataType = "String", paramType = "query")
    })
    public R materialShipDzExport(String id, HttpServletResponse response) {
        orderShipService.materialShipDzExport(id, response);
        return R.success();
    }

    @GetMapping("/exportDataPurchase")
    @ApiOperation(value = "物资入库单模板导出")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "发货单Id", required = true,
            dataType = "String", paramType = "query")
    })
    public R exportDataPurchase(String id, HttpServletResponse response) {
        orderShipService.exportDataPurchase(id, response);
        return R.success();
    }

    @GetMapping("/wxExportDataPurchase")
    @ApiOperation(value = "微信物资入库单模板导出")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "发货单Id", required = true,
            dataType = "String", paramType = "query")
    })
    public R wxExportDataPurchase(String id, HttpServletResponse response) {
        orderShipService.wxExportDataPurchase(id, response);
        return R.success();
    }
}



