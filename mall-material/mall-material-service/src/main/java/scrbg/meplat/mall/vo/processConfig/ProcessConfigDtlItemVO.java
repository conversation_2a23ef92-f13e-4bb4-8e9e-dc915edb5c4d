package scrbg.meplat.mall.vo.processConfig;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.vo.product.website.material.WMaterialBaseVo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-11-04 16:56
 */
@Data
public class ProcessConfigDtlItemVO {

    @ApiModelProperty(value = "流程ID")
    private String processId;

    @ApiModelProperty(value = "流程名称")
    private String processName;

//    @ApiModelProperty(value = "系统名称")
//    private String systemName;
//
//    @ApiModelProperty(value = "系统编号")
//    private String systemNo;
//
//    @ApiModelProperty(value = "备注")
//    private String remark;
    
    @ApiModelProperty(value = "审批流程节点表ID")
    private String processNodeId;
    @ApiModelProperty(value = "节点编号")
    private String nodeNo;
    @ApiModelProperty(value = "节点名称0:提交 1:审核 2:审定")
    private String nodeName;
    @ApiModelProperty(value = "审批流程角色表ID")
    private String processRoleId;
    @ApiModelProperty(value = "角色编号)")
    private String roleNo;
    @ApiModelProperty(value = "角色名称(0:供应商 1:机料内业 2:机料部门/科室 3:电商运营部内业人员 4:电商运营部负责人 5:财务部 6:分管领导)")
    private String roleName;
    @ApiModelProperty(value = "审批流程人员表ID")
    private String processUserId;
    @ApiModelProperty(value = "人员ID")
    private String userId;
    @ApiModelProperty(value = "人员名称")
    private String userName;
}
