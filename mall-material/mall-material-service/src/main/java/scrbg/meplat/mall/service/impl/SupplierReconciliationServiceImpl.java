package scrbg.meplat.mall.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.entity.SupplierReconciliation;
import scrbg.meplat.mall.entity.MaterialReconciliation;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.SupplierReconciliationMapper;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.pcwp.dto.PcwpAcceptanceRequest;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.service.stockManage.InboundSettlementService;
import scrbg.meplat.mall.util.*;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
import scrbg.meplat.mall.vo.supplier.SupplierReconciliationVo;
import scrbg.meplat.mall.entity.InterfaceLogs;
import scrbg.meplat.mall.service.InterfaceLogsService;
import scrbg.meplat.mall.pcwp.PcwpService;
import scrbg.meplat.mall.pcwp.PcwpRes;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @描述：物资验收 服务类
 * @作者: ye
 * @日期: 2023-08-15
 */
@Service
public class SupplierReconciliationServiceImpl extends ServiceImpl<SupplierReconciliationMapper, SupplierReconciliation> implements SupplierReconciliationService {

    @Autowired
    AuditRecordService auditRecordService;

    @Autowired
    MallConfig mallConfig;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;
    @Autowired
    private SupplierReconciliationDtlService supplierReconciliationDtlService;

    @Autowired
    private OrderShipDtlService orderShipDtlService;

    @Autowired
    private OrderReturnItemService orderReturnItemService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private RestTemplateUtils restTemplateUtils;

    @Autowired
    private PcwpService pcwpService;

    @Autowired
    private InterfaceLogsService interfaceLogsService;

    @Autowired
    private PlatformDealFeeDtlService platformDealFeeDtlService;

    @Autowired
    private InboundSettlementService inboundSettlementService;

    // 定义PCWP接口URL常量
    private static final String WRITE_BACK_BILL_QUANTIY_URL = "/thirdapi/writeBackBillQuantiy";
    private static final String WRITE_BACK_BILL_LOCK_QUANTITY_URL = "/thirdapi/writeBackBillLockQuantity";
    private static final String CHECK_OUT_BILL_CON_BEINVALIDATED = "/thirdapi/checkOutBillConBeInvalidated?id=";
    private static final String ROLL_BACK_QUANTITY_DRAFT_URL = "/thirdapi/rollBackQuantityDraft?keyId=";

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SupplierReconciliation> q) {

        UserLogin user = ThreadLocalUtil.getCurrentUser();
        Integer userType = (Integer) jsonObject.get("userType");
        Integer businessType = (Integer) jsonObject.get("businessType");
        String keywords = (String) jsonObject.get("keywords");
        String supplierName = (String) jsonObject.get("supplierName");
        String twoSupplierName = (String) jsonObject.get("twoSupplierName");
        String billNo = (String) jsonObject.get("billNo");
        String startDate = (String) jsonObject.get("startDate");
        String endDate = (String) jsonObject.get("endDate");
        Integer createType = (Integer) jsonObject.get("createType");
        Integer state = (Integer) jsonObject.get("state");
        Integer orderBy = (Integer) jsonObject.get("orderBy");
        switch (userType) {
            case 1:
                q.eq(SupplierReconciliation::getSupplierOrgId, user.getEnterpriseId());
                if (StringUtils.isNotBlank(twoSupplierName)) {
                    q.eq(SupplierReconciliation::getTwoSupplierName, twoSupplierName.trim());
                }
                q.ne(SupplierReconciliation::getState, 5);
                break;
            case 2:
                q.eq(SupplierReconciliation::getTwoSupplierOrgId, user.getEnterpriseId());
                if (StringUtils.isNotBlank(supplierName)) {
                    q.eq(SupplierReconciliation::getSupplierName, supplierName.trim());
                }
                q.ne(SupplierReconciliation::getState, 0);
                break;
            default:
                throw new BusinessException(500, "请选择正确用户类型");
        }
        q.eq(createType != null, SupplierReconciliation::getCreateType, createType);
        q.eq(businessType != null, SupplierReconciliation::getBusinessType, businessType);
        q.eq(state != null, SupplierReconciliation::getState, state);
        if (StringUtils.isNotBlank(billNo)) {
            q.like(SupplierReconciliation::getBillNo, billNo.trim());
        }
        if (StringUtils.isNotBlank(startDate)) {
            q.gt(SupplierReconciliation::getGmtCreate, startDate);
        }
        if (StringUtils.isNotBlank(endDate)) {
            q.lt(SupplierReconciliation::getGmtCreate, endDate);
        }
        if (orderBy != null) {
            if (orderBy == 0) {
                q.orderByDesc(SupplierReconciliation::getGmtCreate);
            }
            if (orderBy == 1) {
                q.orderByDesc(SupplierReconciliation::getStartTime);
            }
            if (orderBy == 2) {
                q.orderByDesc(SupplierReconciliation::getEndTime);
            }
        } else {
            q.orderByDesc(SupplierReconciliation::getGmtCreate);
        }
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(SupplierReconciliation::getSupplierName, keywords.trim())
                        .or()
                        .like(SupplierReconciliation::getTwoSupplierName, keywords.trim())
                        .or()
                        .like(SupplierReconciliation::getBillNo, keywords.trim())
                        .or()
                        .like(SupplierReconciliation::getOrderSn, keywords.trim());
            });
        }
        IPage<SupplierReconciliation> page = this.page(new Query<SupplierReconciliation>().getPage(jsonObject), q
        );
        return new PageUtils(page);
    }

    @Override
    public void create(SupplierReconciliation dto) {
        //调用父类方法即可
        dto.setInvoiceState(0);
        Integer createType = dto.getCreateType();
        //物资公司对账
        if (createType == 1) {
            //状态（0草稿1待提交2待审核3审核通过4审核失败 5供应商保存  7作废）
            if (dto.getState() != null && dto.getState() == 2) {
                dto.setState(2);
            } else {
                dto.setState(0);
            }
            dto.setSupplierName(ThreadLocalUtil.getCurrentUser().getEnterpriseName());
            dto.setSupplierOrgId(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        } else if (createType == 2) {
            dto.setTwoSupplierName(ThreadLocalUtil.getCurrentUser().getEnterpriseName());
            dto.setTwoSupplierOrgId(ThreadLocalUtil.getCurrentUser().getEnterpriseId());

        } else {
            throw new BusinessException(500, "用户新增类型不存在");
        }
        String materialPlanNo = createMaterialPlanNo(dto.getSupplierOrgId());
        dto.setBillNo(materialPlanNo);
        save(dto);
        List<SupplierReconciliationDtl> dtls = dto.getDtl();
        if (dtls != null && dtls.size() > 0) {
            dtls.forEach(dtl -> dtl.setRatePrice(dtl.getPrice()));
            supplierReconciliationDtlService.saveList(dto, dtls);
        } else {
            throw new BusinessException(500, "对账单明细不能为空");
        }
    }

    @Override
    public String createMaterialPlanNo(String orgId) {
        EnterpriseInfo byId = enterpriseInfoService.getById(orgId);
        String shortCode = byId.getShortCode();
        String yyyYmm = DateUtil.getYYYYmmdd(new Date(), "yyyyMMdd");
        String planNo = shortCode + "CLDZ" + yyyYmm;
        List<SupplierReconciliation> list = lambdaQuery().like(SupplierReconciliation::getBillNo, planNo)
                .orderByDesc(MustBaseEntity::getGmtCreate).last("LIMIT 1").list();
        if (list != null && list.size() > 0) {
            String s = list.get(0).getBillNo();
            String substring = s.substring(s.length() - 3);
            int number = Integer.parseInt(substring);
            number++;
            String format = String.format("%03d", number);
            planNo = planNo + format;
        } else {
            planNo = planNo + "001";
        }
        return planNo;
    }

    @Override
    public void update(SupplierReconciliation supplierReconciliation) {

        List<SupplierReconciliationDtl> dtls = supplierReconciliation.getDtl();
        if (dtls != null && dtls.size() > 0) {
            supplierReconciliationDtlService.updateBatchById(dtls);
            BigDecimal noRateAmount = new BigDecimal(0);
            BigDecimal rateAmount = new BigDecimal(0);
            for (SupplierReconciliationDtl dtl : dtls) {

                BigDecimal price = null;
                if (supplierReconciliation.getBusinessType() == 1) {
                    price = getYueDtlprice(supplierReconciliation, dtl);

                } else if (supplierReconciliation.getBusinessType() == 6) {
                    price = getDtlprice(supplierReconciliation, dtl);
                } else if (supplierReconciliation.getBusinessType() == 2) {
                    price = dtl.getPrice();
                }
                BigDecimal amount = dtl.getQuantity().multiply(price).setScale(2, BigDecimal.ROUND_HALF_UP);
                BigDecimal noprice = TaxCalculator.calculateNotTarRateAmount(price, supplierReconciliation.getTaxRate());
                BigDecimal nRateAmount = TaxCalculator.noTarRateItemAmount(amount, noprice, dtl.getQuantity(), supplierReconciliation.getTaxRate());
                rateAmount = rateAmount.add(amount);
                noRateAmount = noRateAmount.add(nRateAmount);
            }
            if (rateAmount.compareTo(supplierReconciliation.getRateAmount()) != 0) {
                throw new BusinessException("含税总金额计算不一致！");
            }
            if (noRateAmount.compareTo(supplierReconciliation.getNoRateAmount()) != 0) {
                throw new BusinessException("不含税总金额计算不一致！");
            }
            supplierReconciliation.setRateAmount(rateAmount);
            supplierReconciliation.setNoRateAmount(noRateAmount);
            super.updateById(supplierReconciliation);
        }
    }

    //计算大宗单价
    private BigDecimal getDtlprice(SupplierReconciliation dto, SupplierReconciliationDtl dtl) {
        BigDecimal price = BigDecimal.valueOf(0);
        if (dto.getType() == 1) {
            BigDecimal netPrice = dtl.getNetPrice();
            BigDecimal fixationPrice = dtl.getFixationPrice();
            price = netPrice.add(fixationPrice);
        } else if (dto.getType() == 2) {
            BigDecimal outFactoryPrice = dtl.getOutFactoryPrice();
            BigDecimal transportPrice = dtl.getTransportPrice();
            price = outFactoryPrice.add(transportPrice);
        }
        if (price.compareTo(dtl.getPrice()) != 0) {
            throw new BusinessException("含税单价计算不一致！");
        }
        return price;
    }

    //计算月供
    private BigDecimal getYueDtlprice(SupplierReconciliation dto, SupplierReconciliationDtl dtl) {
        BigDecimal price = BigDecimal.valueOf(0);
        if (dto.getType() == 1) {
            BigDecimal netPrice = dtl.getNetPrice();
            BigDecimal fixationPrice = dtl.getFixationPrice();
            price = netPrice.add(fixationPrice);
        } else if (dto.getType() == 2) {
            price = dtl.getPrice();
        }
        if (price.compareTo(dtl.getPrice()) != 0) {
            throw new BusinessException("含税单价计算不一致！");
        }
        return price;
    }

    @Override
    public SupplierReconciliation getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        SupplierReconciliation byId = getById(id);
        if (byId.getState() == 3) {
            throw new BusinessException(500, "对账单已经审核通过，不能删除");
        }
        supplierReconciliationDtlService.nullifyDataByBillId(id);
        baseMapper.deleteById(id);
    }

    @Override
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

    @Override
    public SupplierReconciliation getByNo(String billNo) {
        SupplierReconciliation one = lambdaQuery().eq(SupplierReconciliation::getBillNo, billNo).one();
        if (one == null) return one;
        List<SupplierReconciliationDtl> list = supplierReconciliationDtlService.lambdaQuery()
                .eq(SupplierReconciliationDtl::getBillId, one.getBillId())
                .orderByDesc(SupplierReconciliationDtl::getOrderSn,
                        SupplierReconciliationDtl::getQuantity,
                        SupplierReconciliationDtl::getMaterialName,
                        SupplierReconciliationDtl::getRateAmount)
                .list();
        one.setDtl(list);
        List<AuditRecord> auditList = auditRecordService.lambdaQuery()
                .eq(AuditRecord::getRelevanceType, 6)
                .eq(AuditRecord::getRelevanceId, one.getBillId())
                .list();
        one.setAuditRecords(auditList);
        return one;
    }

    @Override
    public void reconciliationSubmit(List<String> ids) {
        List<SupplierReconciliation> list = lambdaQuery().in(SupplierReconciliation::getBillId, ids)
                .select(SupplierReconciliation::getBillId, SupplierReconciliation::getState).list();
        if (!CollectionUtils.isEmpty(list)) {
            for (SupplierReconciliation materialReconciliation : list) {
                Integer state = materialReconciliation.getState();
                if (state == 0) {
                    materialReconciliation.setState(1);
                    update(materialReconciliation);
                }
            }
        }
    }

    @Override
    public void twoSupplierReconciliationSupplierAffirm(String billId) {
        if (StringUtils.isNotBlank(billId)) {
            lambdaUpdate().eq(SupplierReconciliation::getBillId, billId)
                    .eq(SupplierReconciliation::getState, 3)
                    .set(SupplierReconciliation::getTwoSupplierIsAffirm, 1)
                    .set(SupplierReconciliation::getTwoSupplierAffirmTime, new Date())
                    .update();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void twoSupplierReconciliationSupplierCreate(SupplierReconciliation dto) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        dto.setCreateType(2);
        dto.setState(0);
        String supplierOrgId = dto.getSupplierOrgId();
        String materialPlanNo = createMaterialPlanNo(supplierOrgId);
        dto.setBillNo(materialPlanNo);
        dto.setTwoSupplierName(user.getEnterpriseName());
        dto.setTwoSupplierOrgId(user.getEnterpriseId());

        List<SupplierReconciliationDtl> dtls = dto.getDtl();
        if (dtls != null && dtls.size() > 0) {
            supplierReconciliationDtlService.saveList(dto, dtls);
        } else {
            throw new BusinessException(500, "对账单明细不能为空");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateState(String billId, int state) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        SupplierReconciliation byId = getById(billId);
        String keyId = IdWorker.getIdStr();
        switch (state) {
            //供应商提交 为待审核
            case 2:
                if (byId.getState() != 0) throw new BusinessException(500, "对账单不是草稿状态，不能提交");
                byId.setSubmitUserId(user.getUserId());
                byId.setSubmitUserName(user.getUserName());
                byId.setSubmitTime(new Date());
                updateDataState(state, byId);
                break;
            case 3:
                if (byId.getState() != 2) throw new BusinessException(500, "对账单不是待审核状态，不能审核");
                //审核通过记录
                auditRecordService.createDataPass(byId.getBillId(), 6);
                updateDataState(state, byId);
                // 添加PCWP推送功能 - 审核通过时推送验收单
                if (StringUtils.isNotBlank(byId.getRelevanceId())) {
                    pushToPCWP(byId, keyId);
                }
                //入库结算单生成
                createInboundStickSettlement(byId);
                // 添加缴费交易费用接口调用 - 参考MaterialReconciliationServiceImpl.materialReconciliationAuditPlan
                if (mallConfig.isPlatformFee == 1) {
                    // 调用现有的缴费交易费用接口
                    platformDealFeeDtlService.checkTwoDealAmount(byId);
                }
                break;
            case 4:
                if (byId.getState() != 2) throw new BusinessException(500, "对账单不是待审核状态，不能审核");
                auditRecordService.createDataNoPass(byId.getBillId(), 6, "审核不通过");
                updateDataState(state, byId);
                break;
            case 7:
                // 参考 MaterialReconciliationServiceImpl.materialReconciliationCancellation 的作废逻辑
                // 检查是否已结算
                if (byId.getSettleAmount() != null && byId.getSettleAmount().compareTo(BigDecimal.ZERO) > 0) {
                    throw new BusinessException("已结算不能作废操作！");
                }
                // 设置作废信息
                byId.setNullifyCreated(new Date());
                byId.setNullifyCreatorId(user.getUserId());
                byId.setNullifyCreator(user.getUserName());
                byId.setNullifyReason(""); // 可以从参数中获取作废原因
                // 先处理平台费用（在PCWP处理之前）
                if (mallConfig.isPlatformFee == 1) {
                    // 保存交易明细反记录
                    platformDealFeeDtlService.addTwoNegativeFeeCancellation(byId);
                }
                // 处理PCWP作废逻辑
                if (StringUtils.isNotBlank(byId.getRelevanceId())) {
                    cancelPCWPBill(byId, keyId);
                }
                // 更新状态和明细
                updateDataState(state, byId);
                supplierReconciliationDtlService.nullifyDataByBillId(billId);
                // 记录成功日志
                createAndSaveInterfaceLog(keyId, "supplierReconciliationStateUpdate",
                    "billId:" + billId + ",state:" + state, "作废二级供应商对账单", "作废成功", 1, 1);
                break;
            default:
                throw new BusinessException(500, "对账单操作错误，请联系管理员，添加新状态");
        }
        updateById(byId);
    }

    private void createInboundStickSettlement(SupplierReconciliation reconciliation) {
        if(null == reconciliation){
            return;
        }
        List<SupplierReconciliationDtl> dtl = reconciliation.getDtl();
        if(null == dtl || dtl.isEmpty()){
            dtl = supplierReconciliationDtlService.lambdaQuery()
                    .eq(SupplierReconciliationDtl::getBillId, reconciliation.getBillId())
                    .list();
        }
        InboundSettlementManage manage = new InboundSettlementManage();
        manage.setReconciliationId(reconciliation.getBillId());
        manage.setSupplierType(1);
        manage.setSupplierId(reconciliation.getTwoSupplierOrgId());
        manage.setSupplierName(reconciliation.getTwoSupplierName());
        manage.setInboundType(2);
        manage.setContractNo(reconciliation.getOrderSn());
        manage.setInvoiceNum("");
        manage.setRateAmount(reconciliation.getRateAmount());
        manage.setNoRateAmount(reconciliation.getNoRateAmount());
        manage.setNum(dtl.stream().map(SupplierReconciliationDtl:: getQuantity)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        manage.setSettlementInfo(JSON.toJSONString(dtl));
        manage.setStoredWarehouseTime(reconciliation.getTwoSubmitTime());
        inboundSettlementService.saveAndSubmitSettlement(manage);
    }

    private void updateTwoSupplierState(int state, SupplierReconciliation byId) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        if (user.getEnterpriseId().equals(byId.getTwoSupplierOrgId())) {
            if (byId.getState() == 0) {
                byId.setState(state);
                byId.setTwoSubmitTime(new Date());
                return;
            } else {
                throw new BusinessException(500, "对账状态错误");
            }
        } else {
            throw new BusinessException(500, "该企业不是此对账的二级供应商，不能提交");
        }
    }

    private void updateDataState(int state, SupplierReconciliation byId) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        if (user.getEnterpriseId().equals(byId.getSupplierOrgId())) {
            byId.setState(state);
            return;
        } else {
            if (byId.getState() < 2) {
                byId.setState(state);
                return;
            }
            throw new BusinessException(500, "该企业不是自营店，不能提交");
        }
    }

    @Override
    public void supplierReconciliationSupplierAffirm(String billId) {
        SupplierReconciliation byId = getById(billId);
        byId.setSupplierIsAffirm(1);
        byId.setSupplierAffirmTime(new Date());
        updateById(byId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAndUpdate(SupplierReconciliation dto) {
        SupplierReconciliation supplierReconciliation = getById(dto.getBillId());
        if (supplierReconciliation == null) return;
        if (supplierReconciliation.getState() == 2 || supplierReconciliation.getState() == 3) {
            return;
        }
        // 对账总金额
        BigDecimal notTotalAmous = new BigDecimal(0);
        BigDecimal totalAmous = new BigDecimal(0);
        List<SupplierReconciliationDtl> dtls = dto.getDtl();
        if (dtls != null && dtls.size() > 0) {
            for (SupplierReconciliationDtl dtl : dtls) {
                if (dtl.getRatePrice() == null) {
                    dtl.setRateAmount(dtl.getTotalAmount());
                }
                SupplierReconciliationDtl dtlInfo = supplierReconciliationDtlService.getById(dtl.getDtlId());
                BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(dtl.getRatePrice(), dto.getTaxRate());
                dtlInfo.setNoRatePrice(noRatePrice);
                dtlInfo.setState(dtl.getState());
                dtlInfo.setPrice(dtl.getPrice());
                dtlInfo.setNetPrice(dtl.getNetPrice());
                dtlInfo.setFixationPrice(dtl.getFixationPrice());
                dtlInfo.setTransportPrice(dtl.getTransportPrice());
                dtlInfo.setOutFactoryPrice(dtl.getOutFactoryPrice());
                BigDecimal price = null;
                if (dto.getBusinessType() == 1) {
                    price = getYueDtlprice(dto, dtl);
                } else if (dto.getBusinessType() == 6) {
                    price = getDtlprice(dto, dtl);
                } else if (dto.getBusinessType() == 2) {
                    price = dtl.getPrice();
                }
                BigDecimal amount = dtl.getQuantity().multiply(price).setScale(2, BigDecimal.ROUND_HALF_UP);
                BigDecimal noPrice = TaxCalculator.calculateNotTarRateAmount(price, dto.getTaxRate());

                BigDecimal nRateAmount = TaxCalculator.noTarRateItemAmount(amount, noPrice, dtl.getQuantity(), dto.getTaxRate());
//                if (amount.compareTo(dtl.getRateAmount()) != 0) {
//                    throw new BusinessException("含税总金额计算不一致！");
//                }
//                if (nRateAmount.compareTo(dtl.getNoRateAmount()) != 0) {
//                    throw new BusinessException("不含税总金额计算不一致！");
//                }
                dtlInfo.setTotalAmount(dtl.getTotalAmount());
                dtlInfo.setRateAmount(dtl.getRateAmount());
                dtlInfo.setNoRatePrice(dtl.getRatePrice());
//                dtlInfo.setNoRateAmount(notTarRateAmount);
                dtlInfo.setRemarks(dtl.getRemarks());
                supplierReconciliationDtlService.update(dtlInfo);
                totalAmous = totalAmous.add(amount);
                notTotalAmous = notTotalAmous.add(nRateAmount);
            }
            if (totalAmous.compareTo(dto.getRateAmount()) != 0) {
                throw new BusinessException("含税总金额计算不一致！");
            }
            if (notTotalAmous.compareTo(dto.getNoRateAmount()) != 0) {
                throw new BusinessException("不含税总金额计算不一致！");
            }
            dto.setRateAmount(totalAmous);
            dto.setNoRateAmount(notTotalAmous);
            updateById(dto);
        } else {
            throw new BusinessException(500, "对账单明细不能为空");
        }
    }

    @Override
    public void outputExcel(String billId, HttpServletResponse response) {
        String src = mallConfig.templateFormUrl;
        SupplierReconciliation supplierObj = getById(billId);

        // 处理date
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
        String startTimeStr = dateFormat.format(supplierObj.getStartTime());
        String endTimeStr = dateFormat.format(supplierObj.getEndTime());
        Map<String, Object> stringObjectMap = new HashMap<>(16);

        //含税金额
        BigDecimal rateTotalAmount = new BigDecimal("0.00");
        //不含税金额
        BigDecimal noRateTotalAmount = new BigDecimal("0.00");
        ;
        //总税额
        BigDecimal taxAmount = new BigDecimal("0.00");
        ;
        List<SupplierReconciliationDtlExcel> list = supplierReconciliationDtlService.getReceiptDetailByBillId(billId);
//        supplierObj.setDtlExcel(list);
        int index = 0;
        for (SupplierReconciliationDtlExcel dtl : list) {
            index++;
            rateTotalAmount = rateTotalAmount.add(dtl.getRateAmount());
            noRateTotalAmount = noRateTotalAmount.add(dtl.getNoRateAmount());
            dtl.setIndex(index);
            if(dtl.getRatePrice() == null) {
                dtl.setRatePrice(dtl.getPrice());
            }
        }
        // 如果需要保留2位小数
        taxAmount = rateTotalAmount.subtract(noRateTotalAmount).setScale(2, RoundingMode.HALF_UP);
        stringObjectMap.put("supplierName", supplierObj.getSupplierName());//供货单位
        stringObjectMap.put("warehouseName", supplierObj.getTwoSupplierName());//收货单位
        stringObjectMap.put("auditDate", startTimeStr + '-' + endTimeStr);
        stringObjectMap.put("noRateTotalAmount", noRateTotalAmount);
        stringObjectMap.put("rateTotalAmount", rateTotalAmount);
        stringObjectMap.put("taxAmount", taxAmount);
        stringObjectMap.put("dtlExcel", list);

        try {
            ExcelForWebUtil.exportExcel(response, stringObjectMap, "对账单模板.xlsx", src, "物资供货对账单.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException("导出失败！");
        }
    }

    /**
     * 推送对账单到PCWP系统
     *
     * @param supplierReconciliation 对账单信息
     * @param keyId                  唯一标识
     */
    private void pushToPCWP(SupplierReconciliation supplierReconciliation, String keyId) {
        // 判断是否能推送验收单
        Boolean canPush = checkCanPushToPCWP(supplierReconciliation, keyId);
        if (canPush) {
            // 获取对账单明细
            List<SupplierReconciliationDtl> dtls = supplierReconciliationDtlService.lambdaQuery()
                    .eq(SupplierReconciliationDtl::getBillId, supplierReconciliation.getBillId())
                    .list();

            // 创建PCWP验收单请求对象
            PcwpAcceptanceRequest request = new PcwpAcceptanceRequest();
            request.setKeyId(keyId);
            // 构建请求数据对象
            PcwpAcceptanceRequest.PcwpAcceptanceData requestData = buildPcwpAcceptanceData(supplierReconciliation, dtls);
            // 构建请求明细数据
            List<PcwpAcceptanceRequest.PcwpAcceptanceDtl> requestDtls = buildPcwpAcceptanceDtls(dtls);
            requestData.setDtls(requestDtls);
            request.setData(requestData);
            // 记录日志
            LogUtil.writeInfoLog(keyId, "pushToPCWP", supplierReconciliation.getBillId(), request, null, SupplierReconciliationServiceImpl.class);
            // 调用PCWP接口
            PcwpRes<String> r = null;
            try {
                r = pcwpService.saveAcceptance(request);
            } catch (Exception e) {
                // 捕获异常
                LogUtil.writeErrorLog(keyId, "pushToPCWP", supplierReconciliation.getBillId(), request, null, e.getMessage(), SupplierReconciliationServiceImpl.class);
                throw new BusinessException("【远程异常】" + e.getMessage());
            }
            if (r.getCode() == null || r.getCode() != 200) {
                // 返回不是200异常
                LogUtil.writeErrorLog(keyId, "pushToPCWP", supplierReconciliation.getBillId(), request, r, r.getMessage(), SupplierReconciliationServiceImpl.class);
                throw new BusinessException("【远程异常】" + r.getMessage());
            }
            // 拿到验收id
            String pcwpBillId = r.getData().toString();
            lambdaUpdate().eq(SupplierReconciliation::getBillId, supplierReconciliation.getBillId())
                    .set(SupplierReconciliation::getRelevanceId, pcwpBillId)
                    .set(SupplierReconciliation::getIsNotPush, 1)
                    .update();
            // 成功保存日志
            createAndSaveInterfaceLog(keyId, "pushToPCWP",
                JSON.toJSONString(supplierReconciliation.getBillId()), JSON.toJSONString(request),
                JSON.toJSONString(r), 1, 1);
        } else {
            // 不能推送，设置标记
//            lambdaUpdate().eq(SupplierReconciliation::getBillId, supplierReconciliation.getBillId())
//                    .set(SupplierReconciliation::getIsNotPush, 1).update();
        }
    }

    /**
     * 检查是否可以推送到PCWP
     *
     * @param supplierReconciliation 对账单信息
     * @param keyId                  唯一标识
     * @return 是否可以推送
     */
    private Boolean checkCanPushToPCWP(SupplierReconciliation supplierReconciliation, String keyId) {
        Boolean canPush = false;

        // 判断是否能推送验收单
        PcwpRes<Boolean> r11 = null;
        try {
            r11 = pcwpService.isCanOperaBill(supplierReconciliation.getSupplierOrgId(), DateUtil.getymd(LocalDate.now()));
        } catch (Exception e) {
            LogUtil.writeErrorLog(keyId, "checkCanPushToPCWP", supplierReconciliation.getBillId(), null, null, e.getMessage(), SupplierReconciliationServiceImpl.class);
            throw new BusinessException("【远程异常】判断是否可推送验收单：" + e.getMessage());
        }

        if (r11.getCode() == null || r11.getCode() != 200) {
            LogUtil.writeErrorLog(keyId, "checkCanPushToPCWP", supplierReconciliation.getBillId(), null, r11, r11.getMessage(), SupplierReconciliationServiceImpl.class);
            throw new BusinessException("【远程异常】判断是否可推送验收单：" + r11.getMessage());
        }

        // 当返回码为200且data为true时，设置canPush为true
        canPush = r11.getData() != null && r11.getData();
        return canPush;
    }

    /**
     * 作废PCWP单据
     *
     * @param supplierReconciliation 对账单信息
     * @param keyId                  唯一标识
     */
    private void cancelPCWPBill(SupplierReconciliation supplierReconciliation, String keyId) {
        Integer createType = supplierReconciliation.getCreateType();
        String relevanceId = supplierReconciliation.getRelevanceId();

        // 记录日志、处理异常
        log.warn("供应商对账单作废需要校验PCWP是否作废：");

        Boolean canCancel = null;

        // 商城新增需要校验pcwp是否作废
        if (createType == 1 || createType == 2) {
            log.warn("商城新增需要校验pcwp是否作废：");
            if (StringUtils.isBlank(relevanceId)) {
                log.warn("商城新增需要校验pcwp是否作废：因无关联ID，直接可作废");
                canCancel = true;
            } else {
                log.warn("relevanceId: " + relevanceId);
                log.warn("supplierOrgId: " + supplierReconciliation.getSupplierOrgId());
                PcwpRes<Boolean> r = null;
                LogUtil.writeInfoLog(keyId, "cancelPCWPBill", supplierReconciliation.getBillId(), relevanceId, null, SupplierReconciliationServiceImpl.class);
                try {
                    // 判断是否可以作废
                    r = pcwpService.checkOutBillCanBeInvalidated(relevanceId, supplierReconciliation.getSupplierOrgId());
                } catch (Exception e) {
                    LogUtil.writeErrorLog(keyId, "cancelPCWPBill", supplierReconciliation.getBillId(), relevanceId, null, e.getMessage(), SupplierReconciliationServiceImpl.class);
                    throw new BusinessException("【远程异常】检查外部单据是否能作废失败：" + e.getMessage());
                }
                if (r.getCode() == null || r.getCode() != 200) {
                    LogUtil.writeErrorLog(keyId, "cancelPCWPBill", supplierReconciliation.getBillId(), relevanceId, r, r.getMessage(), SupplierReconciliationServiceImpl.class);
                    throw new BusinessException("【远程异常】检查外部单据是否能作废失败：" + r.getMessage());
                }
                canCancel = r.getData();
                log.warn("商城新增需要校验pcwp是否作废接口返回：" + r);
            }
        } else if (createType == 3) {
            // PCWP推送的直接可作废
            canCancel = true;
        } else {
            // 其他类型的对账单，直接返回
            log.warn("非PCWP类型的对账单（createType={}），跳过PCWP作废操作" + createType);
            return;
        }

        if (canCancel) {
            // 检查创建类型和关联ID
            if (createType == 3) {
                if (StringUtils.isNotBlank(relevanceId)) {
                    // 记录详细日志
                    LogUtil.writeInfoLog(keyId, "cancelPCWPBill", relevanceId, relevanceId, null, SupplierReconciliationServiceImpl.class);
                    log.warn("清除PCWP关系请求参数：");
                    log.warn("relevanceId: " + relevanceId);
                    log.warn("supplierOrgId: " + supplierReconciliation.getSupplierOrgId());

                    // 清除PCWP关系
                    PcwpRes<Boolean> r = null;
                    try {
                        r = pcwpService.clearRelationId(relevanceId, supplierReconciliation.getSupplierOrgId());
                    } catch (Exception e) {
                        // 捕获异常
                        LogUtil.writeErrorLog(keyId, "cancelPCWPBill", relevanceId, relevanceId, null, e.getMessage(), SupplierReconciliationServiceImpl.class);
                        throw new BusinessException("【远程异常】清除PCWP关系失败：" + e.getMessage());
                    }

                    if (r.getCode() == null || r.getCode() != 200) {
                        LogUtil.writeErrorLog(keyId, "cancelPCWPBill", relevanceId, relevanceId, r, r.getMessage(), SupplierReconciliationServiceImpl.class);
                        throw new BusinessException("【远程异常】清除PCWP关系失败：" + r.getMessage());
                    }

                    // 成功保存日志
                    createAndSaveInterfaceLog(keyId, "cancelPCWPBill",
                        relevanceId, relevanceId, JSON.toJSONString(r), 1, 1);
                }
            }
        } else {
            throw new BusinessException("PCWP存在有效单据不可作废！");
        }
    }


    /**
     * 主动推送对账单到PCWP
     *
     * @param billId 对账单ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void supplierReconciliationPushAcceptance(String billId) {
        SupplierReconciliation byId = getById(billId);
        if (byId == null) {
            throw new BusinessException("对账单不存在");
        }

        if (byId.getState() != 3) {
            throw new BusinessException("只有审核通过的对账单才能推送");
        }

        if (byId.getCreateType() != 3) {
            throw new BusinessException("只有PCWP类型的对账单才能推送");
        }

        String keyId = IdWorker.getIdStr();
        pushToPCWP(byId, keyId);
    }

    /**
     * 构建PCWP验收单请求数据对象
     *
     * 该方法用于构建推送到PCWP系统的验收单主体数据，包含验收单的基础信息、
     * 财务信息、关联信息等核心数据。
     *
     * @param supplierReconciliation 供应商对账单信息
     * @param dtls 对账明细列表，用于计算汇总数据
     * @return 构建完成的PCWP验收单数据对象
     */
    private PcwpAcceptanceRequest.PcwpAcceptanceData buildPcwpAcceptanceData(
            SupplierReconciliation supplierReconciliation,
            List<SupplierReconciliationDtl> dtls) {

        PcwpAcceptanceRequest.PcwpAcceptanceData requestData = new PcwpAcceptanceRequest.PcwpAcceptanceData();

        // 基础信息
        requestData.setAcceptanceAmount(supplierReconciliation.getNoRateAmount()); // 验收总金额（不含税）
        requestData.setAcceptanceDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))); // 验收日期 (yyyy-MM-dd格式)
        requestData.setAcceptanceQuantity(dtls.stream().mapToDouble(dtl -> dtl.getQuantity().doubleValue()).sum()); // 验收总数量
        requestData.setAcceptancerId(supplierReconciliation.getFounderId()); // 验收人员ID
        requestData.setAcceptancerName(supplierReconciliation.getFounderName()); // 验收人员姓名
        requestData.setBillId(supplierReconciliation.getBillId()); // 单据ID
        requestData.setBillNo(supplierReconciliation.getBillNo()); // 验收单据编号
        requestData.setBusinessType(supplierReconciliation.getBusinessType()); // 业务类型

        // 财务信息
        requestData.setTaxAmount(supplierReconciliation.getTaxAmount() != null ? supplierReconciliation.getTaxAmount() : BigDecimal.ZERO); // 税额
        requestData.setTaxRate(supplierReconciliation.getTaxRate()); // 税率
        requestData.setTotalAmount(supplierReconciliation.getRateAmount()); // 税价合计（含税总金额）

        // 关联信息
        requestData.setFounderId(supplierReconciliation.getFounderId()); // 创建人ID
        requestData.setFounderName(supplierReconciliation.getFounderName()); // 创建人姓名
        requestData.setFreight(BigDecimal.ZERO); // 运输单位费用（默认为0）
        requestData.setOrgId(supplierReconciliation.getSupplierOrgId()); // 机构ID
        requestData.setOrgName(supplierReconciliation.getSupplierName()); // 机构名称
        requestData.setOuterId(supplierReconciliation.getBillId()); // 对账单ID
        requestData.setOuterNo(supplierReconciliation.getBillNo()); // 对账单编号
        requestData.setPurchaserId(supplierReconciliation.getFounderId()); // 采购人员ID（使用创建人ID）
        requestData.setPurchaserName(supplierReconciliation.getFounderName()); // 采购人员姓名（使用创建人姓名）
        requestData.setPurchasingUnitId(supplierReconciliation.getSupplierOrgId()); // 采购单位ID
        requestData.setPurchasingUnitName(supplierReconciliation.getSupplierName()); // 采购单位名称

        // 其他信息
        requestData.setRemarks(supplierReconciliation.getRemarks()); // 备注
        requestData.setSourceBillId(supplierReconciliation.getOrderId()); // 源单ID
        requestData.setSourceBillName(supplierReconciliation.getOrderSn()); // 源单名称（使用订单编号）
        requestData.setSourceBillNo(supplierReconciliation.getOrderSn()); // 源单编号
        requestData.setSupplierId(supplierReconciliation.getTwoSupplierOrgId()); // 供应商ID
        requestData.setSupplierName(supplierReconciliation.getTwoSupplierName()); // 供应商名称
        requestData.setType(supplierReconciliation.getType()); // 清单类型

        return requestData;
    }

    /**
     * 构建PCWP验收单明细数据列表
     *
     * 该方法用于构建推送到PCWP系统的验收单明细数据，将对账明细转换为
     * PCWP系统要求的验收明细格式。
     *
     * @param dtls 对账明细列表
     * @return 构建完成的PCWP验收单明细数据列表
     */
    private List<PcwpAcceptanceRequest.PcwpAcceptanceDtl> buildPcwpAcceptanceDtls(List<SupplierReconciliationDtl> dtls) {
        List<PcwpAcceptanceRequest.PcwpAcceptanceDtl> requestDtls = new ArrayList<>();

        for (SupplierReconciliationDtl dtl : dtls) {
            PcwpAcceptanceRequest.PcwpAcceptanceDtl requestDtl = new PcwpAcceptanceRequest.PcwpAcceptanceDtl();

            // 验收信息
            requestDtl.setAcceptanceAmount(dtl.getNoRateAmount()); // 本次验收金额
            requestDtl.setAcceptanceQuantity(dtl.getQuantity()); // 本次验收数量
            requestDtl.setBillId(dtl.getBillId()); // 关联验收单ID
            requestDtl.setDtlId(dtl.getDtlId()); // 验收明细ID

            // 价格信息
            requestDtl.setFactoryPrice(dtl.getOutFactoryPrice() != null ? dtl.getOutFactoryPrice() : BigDecimal.ZERO); // 出厂价
            requestDtl.setFixedFee(dtl.getFixationPrice() != null ? dtl.getFixationPrice() : BigDecimal.ZERO); // 固定价
            requestDtl.setFreight(dtl.getTransportPrice() != null ? dtl.getTransportPrice() : BigDecimal.ZERO); // 运费
            requestDtl.setNetworkPrice(dtl.getNetPrice() != null ? dtl.getNetPrice() : BigDecimal.ZERO); // 网价
            requestDtl.setPrice(dtl.getPrice()); // 物资单价
            requestDtl.setTaxAmount(dtl.getTaxAmount() != null ? dtl.getTaxAmount() : BigDecimal.ZERO); // 税额

            // 物资信息
            requestDtl.setMaterialClassId(dtl.getMaterialClassId()); // 物资类别ID
            requestDtl.setMaterialClassName(dtl.getMaterialClassName()); // 物资类别名称
            requestDtl.setMaterialId(dtl.getMaterialId()); // 物资ID
            requestDtl.setMaterialName(dtl.getMaterialName()); // 物资名称
            requestDtl.setSpec(dtl.getSpec()); // 规格型号
            requestDtl.setTexture(dtl.getTexture()); // 材质
            requestDtl.setUnit(dtl.getUnit()); // 计量单位

            // 关联信息
            requestDtl.setOrderId(dtl.getOrderId()); // 关联订单ID
            requestDtl.setOrderNo(dtl.getOrderSn()); // 关联订单编号
            requestDtl.setSourceDtlId(dtl.getSourceDtlId()); // 源单明细ID
            requestDtl.setTradeId(dtl.getMaterialId()); // 商品ID（使用物资ID）

            // 默认值
            requestDtl.setNotAcceptedQuantity(BigDecimal.ZERO); // 未验收数量（默认为0）
            requestDtl.setWarehouseId(""); // 仓库ID（暂时为空）
            requestDtl.setWarehouseName(""); // 仓库名称（暂时为空）

            requestDtls.add(requestDtl);
        }

        return requestDtls;
    }

    /**
     * 创建并保存接口日志
     *
     * @param secretKey      秘钥唯一key
     * @param methodName     方法名
     * @param localArguments 本地方法请求参数
     * @param farArguments   请求远程接口参数
     * @param result         返回结果
     * @param isSuccess      是否成功（0否1是）
     * @param logType        日志类型（1请求远程2请求远程回滚3请求本地4请求本地回滚）
     */
    private void createAndSaveInterfaceLog(String secretKey, String methodName, String localArguments,
                                           String farArguments, String result, Integer isSuccess, Integer logType) {
        InterfaceLogs iLog = new InterfaceLogs();
        iLog.setSecretKey(secretKey);
        iLog.setClassPackage(SupplierReconciliationServiceImpl.class.getName());
        iLog.setMethodName(methodName);
        iLog.setLocalArguments(localArguments);
        iLog.setFarArguments(farArguments);
        iLog.setIsSuccess(isSuccess);
        iLog.setLogType(logType);
        iLog.setResult(result);
        interfaceLogsService.create(iLog);
    }
}
