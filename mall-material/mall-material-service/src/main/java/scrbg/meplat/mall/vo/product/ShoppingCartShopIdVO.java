package scrbg.meplat.mall.vo.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.ShoppingCart;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-11-13 16:05
 */
@Data
public class ShoppingCartShopIdVO {

    private String shopId;//商铺id

    private List<ShoppingCart> shoppingCarts;//按照购物车店铺下的商品列表信息

    @ApiModelProperty(value = "订单总价格")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "实际支付总价格")
    private BigDecimal actualAmount;

    @ApiModelProperty(value = "总成本价")
    private BigDecimal costPriceTotal;
}
