package scrbg.meplat.mall.pcwp.third.model;

import java.math.BigDecimal;
// import java.time.LocalDateTime; // 如果需要精确到Java 8日期时间类型，可以启用此行并修改planDate类型

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 大宗零购计划分页查询返回类。
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BulkRetailPlanPageQueryResult {
    private Integer associationType; // 关联状态(1:未关联;2:已关联)
    private String billId;           // 大宗零购计划id
    private String billNo;           // 大宗零购计划编号
    private String creditCode;       // 信用代码
    private String founderId;        // 创建人Id
    private String founderName;      // 创建人名称
    private Integer isOut;           // 是否外部系统添加数据
    private String orgName;          // 单据机构名称
    private String orgShort;         // 供应商机构短码
    private String planDate;         // 计划日期 (Swagger中为date-time格式，这里使用String兼容，也可考虑LocalDateTime)
    private BigDecimal settledAmount; // 已结算金额
    private String state;            // 状态
    private String supplierId;       // 供应商id
    private String supplierName;     // 供应商名称
    private BigDecimal totalAmount;  // 计划金额
}
