package scrbg.meplat.mall.service.impl;

import com.alibaba.excel.util.BeanMapUtils;
import com.baomidou.mybatisplus.core.toolkit.BeanUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.datetime.DateFormatter;
import org.springframework.util.ObjectUtils;
import scrbg.meplat.mall.common.Query;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.BiddingBidRecord;
import scrbg.meplat.mall.entity.BiddingBidRecord;
import scrbg.meplat.mall.entity.BiddingPurchase;
import scrbg.meplat.mall.entity.SynthesizeTemporary;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.BiddingBidRecordMapper;
import scrbg.meplat.mall.service.BiddingBidRecordService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.service.BiddingProductService;
import scrbg.meplat.mall.service.BiddingPurchaseService;
import scrbg.meplat.mall.service.SynthesizeTemporaryService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
import scrbg.meplat.mall.util.pageUtils;
import scrbg.meplat.mall.util.poi.exp.PoiExporter;
import scrbg.meplat.mall.vo.bidding.BidSummarySubVo;
import scrbg.meplat.mall.vo.bidding.GetBidingRecordItemVO;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.SimpleFormatter;
import java.util.stream.Collectors;

/**
 * @描述：竞价记录 服务类
 * @作者: ye
 * @日期: 2023-07-11
 */
@Service
public class BiddingBidRecordServiceImpl extends ServiceImpl<BiddingBidRecordMapper, BiddingBidRecord> implements BiddingBidRecordService{
    @Resource
    private BiddingProductService biddingProductService;
    @Autowired
    private MallConfig mallConfig;
    @Resource
    private BiddingPurchaseService biddingPurchaseService;
    @Resource
    private SynthesizeTemporaryService synthesizeTemporaryService;

    @Autowired
    BiddingBidRecordMapper bidRecordMapper;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingBidRecord> queryWrapper) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        queryWrapper.eq(BiddingBidRecord::getSupplierId,user.getEnterpriseId());
        Map<String, Object> innerMap = jsonObject.getInnerMap();
            String supplierName = (String) innerMap.get("supplierName");
            String biddingId = (String) innerMap.get("biddingId");
            Integer state = (Integer) innerMap.get("state");
            BigDecimal tallbidPrice = (BigDecimal) innerMap.get("tallbidPrice");
            BigDecimal endbidPrice = (BigDecimal) innerMap.get("lowbidPrice");

            String keywords = (String) innerMap.get("keywords");
            String staBidTime = (String) innerMap.get("staBidTime");
            String endBidTime = (String) innerMap.get("endBidTime");
            queryWrapper.eq(supplierName!=null,BiddingBidRecord::getSupplierName,supplierName);
//            queryWrapper.gt(tallbidPrice!=null,BiddingBidRecord::getBidPrice,tallbidPrice);
//            queryWrapper.lt(endbidPrice!=null,BiddingBidRecord::getBidPrice,endbidPrice);
//            queryWrapper.lt(endbidPrice!=null,BiddingBidRecord::getBidPrice,endbidPrice);
            queryWrapper.lt(biddingId!=null,BiddingBidRecord::getBiddingId,biddingId);
            if (staBidTime!=null&&staBidTime!=""){
                queryWrapper.gt(BiddingBidRecord::getBidTime,staBidTime);
            }
            if (endBidTime!=null&&endBidTime!=""){
                queryWrapper.lt(BiddingBidRecord::getBidTime,endBidTime);
            }
            if (state != null){
                queryWrapper.eq(BiddingBidRecord::getState,state);
            }
            if(StringUtils.isNotBlank(keywords)){
                queryWrapper.and((t) -> {
                    t.like(BiddingBidRecord::getSupplierName,keywords);
                });
            }

//
//            IPage<BiddingBidRecord> page = this.page(
//        new Query<BiddingBidRecord>().getPage(jsonObject),
//        queryWrapper
//        );
        Page<BiddingBidRecord> page = new Query<BiddingBidRecord>(innerMap).getPage();
        List<BiddingBidRecord> biddingBidRecords = bidRecordMapper.selectListPaging(page, innerMap);
        page.setRecords(biddingBidRecords);
        return new PageUtils(page);
    }

    @Override
    public void create(BiddingBidRecord biddingBidRecord) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(biddingBidRecord);
    }

    @Override
    public void update(BiddingBidRecord biddingBidRecord) {
        super.updateById(biddingBidRecord);
    }


    @Override
    public BiddingBidRecord getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public List<BiddingBidRecord> selectListByBiddingId(String biddingId) {
        LambdaQueryWrapper<BiddingBidRecord> eq = new LambdaQueryWrapper<BiddingBidRecord>().eq(BiddingBidRecord::getBiddingId, biddingId);
        List<BiddingBidRecord> list = list(eq);
        return  list;
    }

    @Override
    public void exportBidSummary(String bidSn, HttpServletResponse response) {
        if (StringUtils.isBlank(bidSn)){
            throw new BusinessException ("未携带竞价编号");
        }
        Map<String,Object> data = new HashMap<>();
        // 竞价数据
        BiddingPurchase purchase = biddingPurchaseService.lambdaQuery()
                .eq(BiddingPurchase::getBiddingSn, bidSn)
                .and(wra->{
                wra.eq(BiddingPurchase::getBiddingState, 3).or(rapper->{
                    rapper.lt(BiddingPurchase::getEndTime, new Date());
                });
                })
              .one();
        if (ObjectUtils.isEmpty(purchase)){
            throw new BusinessException ("竞价未结束，无法导出");
        }
        // 中标待审核的记录 ？ 是否
        BiddingBidRecord record = super.lambdaQuery().eq(BiddingBidRecord::getBiddingSn, bidSn).eq(BiddingBidRecord::getState, 5).one();
        String hitSupplier = null;
        if (ObjectUtils.isEmpty(record)){
            //throw new BusinessException("未选择中标供货商");
            hitSupplier  = "";

        }else {
            hitSupplier  = record.getSupplierName();
        }
        data.put("hitSupplier",hitSupplier);
        // 中标供货商

        data.put("title",purchase.getCreateOrgName());
        data.put("bidSn",purchase.getBiddingSn());
        // 时间
        Date endTime = purchase.getEndTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String endStr = format.format(endTime);
        String[] strings = endStr.split("-");
        data.put("year",strings[0]);
        data.put("month",strings[1]);
        data.put("day",strings[2]);
        // 报价数据
        List<BidSummarySubVo> vos = super.baseMapper.getBidSummaryWithBidRecordId(bidSn);
        List<BidSummarySubVo> newVos = new ArrayList<>();
        Map<String, List<BidSummarySubVo>> collect = vos.stream().collect(Collectors.groupingBy(BidSummarySubVo::getSupplierName));
        collect.forEach((supplier,list)->{
            // 循环放入
            newVos.addAll(list);
            BigDecimal amount = new BigDecimal(0);
            for (BidSummarySubVo bidSummarySubVo : list) {
                BigDecimal bidRateAmount = bidSummarySubVo.getBidRateAmount();
                amount = amount .add(bidRateAmount);
            }
                BidSummarySubVo bidSummarySubVo = new BidSummarySubVo();
                bidSummarySubVo.setSupplierName(supplier);
                bidSummarySubVo.setProductName("小计");
                bidSummarySubVo.setBidRateAmount(amount);
                newVos.add(bidSummarySubVo);
        });
        ArrayList<BidSummarySubVo> list = Lists.newArrayList(newVos);
        data.put("dataList",list);
        //String src = "/Volumes/westDisk/work/template/竞价模板";
        String src = mallConfig.templateFormUrl;
        if (purchase.getBiddingSourceType() == 3) {
            // 项目部名称
            String synthesizeTemporarySn = purchase.getSynthesizeTemporarySn();
            SynthesizeTemporary synthesizeTemporary = synthesizeTemporaryService.lambdaQuery().eq(SynthesizeTemporary::getSynthesizeTemporarySn, synthesizeTemporarySn).select(SynthesizeTemporary::getOrgName).one();
            if (synthesizeTemporary!= null){
                data.put("title",synthesizeTemporary.getOrgName());
            }
            try {
                  ExcelForWebUtil.exportExcel(response,data,"大宗临购清单竞价汇总模板.xlsx",src,purchase.getTitle()+"竞价汇总.xlsx");
                  return;
            }  catch (Exception e) {
                throw new BusinessException("导出异常"+e.getMessage());
            }
        }
        try {
            ExcelForWebUtil.exportExcel(response,data,"竞价汇总表模板.xlsx",src,purchase.getTitle()+"竞价汇总.xlsx");
        }  catch (Exception e) {
            throw new BusinessException("导出异常"+e.getMessage());
        }
    }
}
