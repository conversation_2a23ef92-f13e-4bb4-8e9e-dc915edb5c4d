package scrbg.meplat.mall.controller.website.userCenter;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.config.auth.IsRole;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.product.material.lcProduct.ProductCardZone;
import scrbg.meplat.mall.entity.ShoppingCart;
import scrbg.meplat.mall.service.ShoppingCartService;
import scrbg.meplat.mall.vo.product.website.WCartInfoVO;
import scrbg.meplat.mall.vo.user.userCenter.IsSynthesizeTemporaryVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * @描述：购物车控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/userCenter/shoppingCart")
@ApiSort(value = 200)
@Api(tags = "购物车（前台）")
public class UserCenterShoppingCartController {

    @Autowired
    public ShoppingCartService shoppingCartService;

    @GetMapping("/addCart")
    @ApiOperation(value = "添加购物车")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "商品id", required = true,
                    dataType = "String"),
            @ApiImplicitParam(name = "cartNum", value = "购买数量", required = true,
                    dataType = "BigDecimal")
    })
    public R addCart(String productId, BigDecimal cartNum,
                     @RequestParam(value = "zoneId",required = false) String zoneId,
                     @RequestParam(value = "zoneAddr",required = false) String zoneAddr,
                     @RequestParam(value = "paymentPeriod",required = false) Integer paymentPeriod) {
        shoppingCartService.addCart(productId, cartNum,zoneId,zoneAddr,paymentPeriod);
        return R.success();
    }

    @ApiOperation(value = "添加购物车")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "商品id", required = true,
                    dataType = "String"),
            @ApiImplicitParam(name = "cartNum", value = "购买数量", required = true,
                    dataType = "BigDecimal")
    })
    @PostMapping("/addCartZone")
    @Transactional
    public R addCartZone(@RequestBody ProductCardZone productCardZone) {
        shoppingCartService.addCartZone(productCardZone);
        return R.success();
    }

    @PostMapping("/updateCartInfo")
    @ApiOperation(value = "修改购物车物品账期等信息")
    public R update(@RequestBody ShoppingCart shoppingCart) {
        System.out.println("Received cartId: " + shoppingCart.getCartId());  // 检查是否为null
        shoppingCartService.update(shoppingCart);
        return R.success();
    }

    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        shoppingCartService.removeRealByIds(ids);
        return R.success();
    }

    @GetMapping("/empty")
    @ApiOperation(value = "清空购物车")
    @ApiImplicitParams({@ApiImplicitParam(name = "productType", value = "商品类型", required = true,
            dataType = "Integer", paramType = "query")
    })
    public R emptyCart(Integer productType) {
        shoppingCartService.emptyCart(productType);
        return R.success();
    }

    @GetMapping("/countCart")
    @ApiOperation(value = "修改购物车数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "购物车id", required = true,
                    dataType = "String"),
            @ApiImplicitParam(name = "changeNum", value = "购物车新数量", required = true,
                    dataType = "BigDecimal")
    })
    public R changeCartNum(String id, BigDecimal changeNum) {
        shoppingCartService.changeCartNum(id, changeNum);
        return R.success();
    }

    @GetMapping("/updateChecked")
    @ApiOperation(value = "修改购物车选中状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "购物车id", required = true,
                    dataType = "String"),
            @ApiImplicitParam(name = "checked", value = "是否选中（1是0否）", required = true,
                    dataType = "Integer")
    })
    public R updateChecked(String id, Integer checked) {
        shoppingCartService.updateChecked(id, checked);
        return R.success();
    }

    @GetMapping("/leaseCountCart")
    @ApiOperation(value = "修改购物车租赁时长")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "购物车id", required = true,
                    dataType = "String"),
            @ApiImplicitParam(name = "changeNum", value = "购物车新租赁时长", required = true,
                    dataType = "BigDecimal")
    })
    public R leaseCountCart(String id, BigDecimal changeNum) {
        shoppingCartService.leaseCountCart(id, changeNum);
        return R.success();
    }

    @GetMapping("/listCart")
    @ApiOperation(value = "获取购物车列表")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "productType", value = "分类类型：0物资 1设备 2周材（物资） 3周材（设备） 4二手设备 5租赁设备",
//                    dataType = "Integer", paramType = "query",required = true),
//    })
    public R<List<WCartInfoVO>> listCart() {
        List<WCartInfoVO> vos = shoppingCartService.listCart(null);
        return R.success(vos);
    }

    @GetMapping("/getCartNum")
    @ApiOperation(value = "获取当前用户的购物车数量")
    public R getCartNum() {
        int count = shoppingCartService.getCartNum();
        return R.success(count);
    }

    @PostMapping("/check/isSynthesizeTemporary")
    @ApiOperation(value = "检查购物车商品是否可创建大宗临购")
    @NotResubmit(delaySeconds = 2)
    @IsRole(roleName = RoleEnum.ROLE_3)
    public R<IsSynthesizeTemporaryVO> isSynthesizeTemporary(@RequestBody List<String> ids) {
        IsSynthesizeTemporaryVO vo = shoppingCartService.isSynthesizeTemporary(ids);
        vo.setCartIds(ids);
        return R.success(vo);
    }
}