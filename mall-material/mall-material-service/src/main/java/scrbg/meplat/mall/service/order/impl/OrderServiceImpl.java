package scrbg.meplat.mall.service.order.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.Data;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.dto.order.ProductBuyInfoDTO;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.OrderSelectPlan;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.ProductSku;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.entity.plan.PlanDetail;
import scrbg.meplat.mall.enums.product.OrderEnum;
import scrbg.meplat.mall.enums.product.ProductEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.OrdersMapper;
import scrbg.meplat.mall.pcwp.KeyedPayload;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.PcwpService;
import scrbg.meplat.mall.pcwp.third.model.UpdatePlanDtl;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.OrderItemService;
import scrbg.meplat.mall.service.OrderSelectPlanService;
import scrbg.meplat.mall.service.ProductCategoryService;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.service.ProductSkuService;
import scrbg.meplat.mall.service.ShopService;
import scrbg.meplat.mall.service.order.OrderService;
import scrbg.meplat.mall.service.plan.PlanDetailService;
import scrbg.meplat.mall.service.plan.PlanService;
import scrbg.meplat.mall.util.OrderUtils;
import scrbg.meplat.mall.util.TaxCalculator;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.product.material.CategoryClassIdAndClassNameVO;

@Service
public class OrderServiceImpl extends ServiceImpl<OrdersMapper, Orders> implements OrderService {

    @Autowired
    private OrderSelectPlanService orderSelectPlanService;
    @Autowired
    private ProductCategoryService productCategoryService;

    @Autowired
    private ProductSkuService productSkuService;

    @Autowired
    private OrderItemService orderItemService;

    @Autowired
    private EnterpriseInfoService enterpriseInfoService;

    @Autowired
    private ProductService productService;

    @Autowired
    private ShopService shopService;

    @Autowired
    private MallConfig mallConfig;

    @Autowired
    private PcwpService pcwpService;

    @Autowired
    private PlanService planService;

    @Autowired
    private PlanDetailService planDetailService;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void createMaterialOrder(List<ProductBuyInfoDTO> dtos, Plan plan) {
        int planType = plan.getType();
        String idStr = IdWorker.getIdStr();
        Map<String, List<ProductBuyInfoDTO>> shopIdGroup = dtos.stream()
                .collect(Collectors.groupingBy(ProductBuyInfoDTO::getShopId));
        shopIdGroup.forEach((shopId, shopIdGroupList) -> {
            createMaterialOrder(shopIdGroupList,shopId,idStr);
        });
        List<PlanDetail> planDetails = planDetailService.lambdaQuery().eq(PlanDetail::getBillId, plan.getBillId()).list();
        for (PlanDetail planDetail : planDetails) {
            Optional<ProductBuyInfoDTO> opbi = dtos.stream().filter(d->d.getDtlId().equals(planDetail.getDtlId())).findAny();
            if (!opbi.isPresent()) {
                continue;
            }
            ProductBuyInfoDTO productBuyInfo = opbi.get();
            Integer consumeNumber = planDetail.getConsumeNumber();
            Integer notConsumeNumber = planDetail.getNotConsumeNumber();
            int cartNum = productBuyInfo.getCartNum().intValue();
            consumeNumber = consumeNumber + cartNum;
            notConsumeNumber = notConsumeNumber - cartNum;
            BigDecimal taxPrice = planDetail.getTaxPrice();
            BigDecimal consumeAmount = taxPrice.multiply(new BigDecimal(consumeNumber));
            BigDecimal notConsumeAmount = taxPrice.multiply(new BigDecimal(notConsumeNumber));
            planDetail.setConsumeNumber(consumeNumber);
            planDetail.setNotConsumeNumber(notConsumeNumber);
            planDetail.setConsumeAmount(consumeAmount);
            planDetail.setNotConsumeAmount(notConsumeAmount);
        }
        // 更新本地计划里的 已消耗金额 consumeAmount; 未消耗金额 notConsumeAmount; 已消耗数量 consumeNumber; 未消耗数量 notConsumeNumber;四项的值
        planDetailService.updateBatchById(planDetails);
        // 计划已完成,更新计划状态为已完成
        if (planDetails.stream().filter(p->p.getNotConsumeNumber().intValue()!=0).count()==0) {
            plan.setState("3");
            planService.updateById(plan);
        }
        List<UpdatePlanDtl> updatePlanDtls = getUpdatePlanDtl(dtos, plan.getPBillId(), planDetails);

        PcwpRes<Void> res;
        try {
            // 这里要根据计划类型判断调用哪个接口（零星，大宗，周材）
            if (planType == 0) {
                res = pcwpService.updateRetailPlanDtl(KeyedPayload.<List<UpdatePlanDtl>>builder()
                                                        .data(updatePlanDtls)
                                                        .keyId(idStr)
                                                        .orgId(ThreadLocalUtil.getCurrentUser().getOrgId())
                                                        .build());
            }else if (planType == 1) {
                res = pcwpService.updateBulkRetailPlanDtl(KeyedPayload.<List<UpdatePlanDtl>>builder()
                                                        .data(updatePlanDtls)
                                                        .keyId(idStr)
                                                        .orgId(ThreadLocalUtil.getCurrentUser().getOrgId())
                                                        .build());
            }else if (planType == 2) {
                res = pcwpService.updateRevolPlanDtl(KeyedPayload.<List<UpdatePlanDtl>>builder()
                                                        .data(updatePlanDtls)
                                                        .keyId(idStr)
                                                        .orgId(ThreadLocalUtil.getCurrentUser().getOrgId())
                                                        .build());
            }else {
                throw new BusinessException("未知的计划类型" + planType);
            }
        } catch (Exception e) {
            log.error("调用反写计划接口异常：", e);
            throw new BusinessException("远程接口调用异常！" + e.getMessage());
        }
        if (res.getCode() == null || res.getCode() != 200) {
            log.error("反写计划接口返回错误：" + res.getMessage());
            throw new BusinessException("远程接口调用失败！" + res.getMessage());
        }

    }


    private void createMaterialOrder(List<ProductBuyInfoDTO> dtos, String shopId, String idStr) {
        // 参数校验
        validateCreateOrderParams(dtos, shopId);

        // 获取店铺信息
        Shop shop = getAndValidateShop(shopId);

        // 获取企业信息
        EnterpriseInfo enterpriseInfo = getEnterpriseInfo(shop.getEnterpriseId());

        // 处理商品信息并计算金额
        OrderCalculationResult calculationResult = processProductsAndCalculateAmounts(dtos);

        // 创建主订单
        Orders mainOrder = createMainOrder(dtos, shop, enterpriseInfo, calculationResult, idStr);

        // 创建订单明细
        List<OrderItem> orderItems = createOrderItems(dtos, mainOrder);

        // 处理库存扣减
        // 库存在生成计划的时候已经扣减
        // processInventoryDeduction(dtos, calculationResult.getProductSkus(), calculationResult.getOutProductIds());

        // 处理拆单（多供方订单）
        if (shop.getShopClass() == 2) {
            processOrderSplitting(mainOrder, orderItems, enterpriseInfo.getTaxRate());
        }

        // 保存计划订单关联信息
        List<OrderSelectPlan> orderSelectPlans = createOrderSelectPlans(dtos, mainOrder, orderItems);
        // 修改数据
        orderSelectPlanService.saveBatch(orderSelectPlans);
    }

    /**
     * 参数校验
     */
    private void validateCreateOrderParams(List<ProductBuyInfoDTO> dtos, String shopId) {
        if (CollectionUtils.isEmpty(dtos)) {
            throw new BusinessException("商品信息不能为空！");
        }
        if (StringUtils.isEmpty(shopId)) {
            throw new BusinessException("店铺ID不能为空！");
        }
    }

    /**
     * 获取并校验店铺信息
     */
    private Shop getAndValidateShop(String shopId) {
        Shop shop = shopService.getById(shopId);
        if (shop == null) {
            throw new BusinessException("店铺不存在！");
        }
        return shop;
    }

    /**
     * 获取企业信息
     */
    private EnterpriseInfo getEnterpriseInfo(String enterpriseId) {
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                .eq(EnterpriseInfo::getEnterpriseId, enterpriseId)
                .select(EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getTaxRate)
                .one();

        if (enterpriseInfo == null) {
            throw new BusinessException("企业信息不存在！");
        }
        return enterpriseInfo;
    }

    /**
     * 处理商品信息并计算金额
     */
    private OrderCalculationResult processProductsAndCalculateAmounts(List<ProductBuyInfoDTO> dtos) {
        OrderCalculationResult result = new OrderCalculationResult();
        BigDecimal actualAmount = BigDecimal.ZERO;
        BigDecimal noRateActualAmount = BigDecimal.ZERO;
        BigDecimal costPriceTotal = BigDecimal.ZERO;

        StringBuilder productNames = new StringBuilder();
        HashSet<String> supplierIdSet = new HashSet<>();
        List<ProductSku> productSkusToUpdate = new ArrayList<>();
        List<String> outProductIds = new ArrayList<>();

        for (ProductBuyInfoDTO dto : dtos) {
            // 验证商品状态
            Product product = validateAndGetProduct(dto.getProductId());
            ProductSku productSku = getProductSku(dto.getProductId());

            // 验证库存充足性
            validateInventory(product, productSku, dto.getCartNum());

            // 收集供应商ID
            supplierIdSet.add(product.getSupperBy());

            // 拼接商品名称
            if (productNames.length() > 0) {
                productNames.append(",");
            }
            productNames.append(product.getProductName());

            // 计算金额
            BigDecimal taxRate = product.getTaxRate();
            // 税率从商品基本信息中获取
            dto.setTaxRate(taxRate);
            actualAmount = actualAmount
                    .add(dto.getSellPrice().multiply(dto.getCartNum()).setScale(2, RoundingMode.HALF_UP));

            BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(dto.getSellPrice(), taxRate);
            BigDecimal notRateAmount = TaxCalculator.noTarRateItemAmount(
                    productSku.getSellPrice().multiply(dto.getCartNum()), noRatePrice, dto.getCartNum(), taxRate);
            noRateActualAmount = noRateActualAmount.add(notRateAmount);

            costPriceTotal = costPriceTotal
                    .add(productSku.getCostPrice().multiply(dto.getCartNum()).setScale(2, RoundingMode.HALF_UP));

            // 准备库存扣减数据
            BigDecimal newStock = productSku.getStock().subtract(dto.getCartNum());
            ProductSku skuToUpdate = new ProductSku();
            skuToUpdate.setSkuId(productSku.getSkuId());
            skuToUpdate.setStock(newStock);
            productSkusToUpdate.add(skuToUpdate);

            // 检查是否需要下架
            if (newStock.compareTo(BigDecimal.ZERO) == 0) {
                outProductIds.add(dto.getProductId());
            }
        }

        result.setActualAmount(actualAmount);
        result.setNoRateActualAmount(noRateActualAmount);
        result.setCostPriceTotal(costPriceTotal);
        result.setProductNames(productNames.toString());
        result.setSupplierIdSet(supplierIdSet);
        result.setProductSkus(productSkusToUpdate);
        result.setOutProductIds(outProductIds);

        return result;
    }

    /**
     * 验证并获取商品信息
     */
    private Product validateAndGetProduct(String productId) {
        Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());

        if (product == null) {
            Product byId = productService.getProductExcludeRemarkById(productId);
            if (byId != null) {
                throw new BusinessException("商品名为：【" + byId.getProductName() + "】的商品已下架或已被删除！");
            } else {
                throw new BusinessException(OrderEnum.RESULT_CODE_500201.getRemark());
            }
        }
        return product;
    }

    /**
     * 获取商品SKU信息
     */
    private ProductSku getProductSku(String productId) {
        List<ProductSku> skus = productSkuService.getProductSkuByProductId(productId, null);
        if (CollectionUtils.isEmpty(skus)) {
            throw new BusinessException("商品SKU信息不存在！");
        }
        return skus.get(0);
    }

    /**
     * 验证库存
     */
    private void validateInventory(Product product, ProductSku productSku, BigDecimal cartNum) {
        if (cartNum.compareTo(productSku.getStock()) > 0) {
            throw new BusinessException("商品名为：【" + product.getProductName() + "】的商品库存不足！");
        }
    }

    /**
     * 创建主订单
     */
    private Orders createMainOrder(List<ProductBuyInfoDTO> dtos, Shop shop, EnterpriseInfo enterpriseInfo,
            OrderCalculationResult calculationResult, String idStr) {
        Orders order = new Orders();
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        ProductBuyInfoDTO firstDto = dtos.get(0);
        Product product = productService.getProductById(firstDto.getProductId(), null);

        // 设置基本信息
        order.setEnterpriseId(currentUser.getEnterpriseId());
        order.setEnterpriseName(currentUser.getEnterpriseName());
        order.setUserId(currentUser.getUserId());
        order.setOrgId(currentUser.getOrgId());
        order.setOrderSn(OrderUtils.getOrder());

        // 设置收货信息
        order.setReceiverName(firstDto.getReceiverName());
        order.setPayWay(firstDto.getPayWay());
        order.setReceiverMobile(firstDto.getReceiverMobile());
        order.setReceiverAddress(firstDto.getReceiverAddress());
        order.setOrderRemark(firstDto.getOrderRemark());

        // 设置店铺信息
        order.setShopId(shop.getShopId());
        order.setShopName(shop.getShopName());
        order.setUntitled(calculationResult.getProductNames());

        // 设置订单类型和供应商信息
        setOrderClassAndSupplier(order, shop, dtos);

        // 设置金额信息
        order.setActualAmount(calculationResult.getActualAmount());
        order.setTotalAmount(BigDecimal.ZERO);
        order.setCostPriceTotal(calculationResult.getCostPriceTotal());
        order.setProfitPriceTotal(calculationResult.getActualAmount().subtract(calculationResult.getCostPriceTotal()));
        // TODO 这里已经改为同一店铺下不同商品税率有可能不同，（原来同一店铺下商品税率相同） 所以这里order不应该再有税率
        // order.setTaxRate(calculationResult.getTaxRate());

        // 设置不含税金额
        if (mallConfig.isNotRateAmount == 1) {
            order.setNoRateAmount(calculationResult.getNoRateActualAmount());
        } else {
            order.setNoRateAmount(calculationResult.getNoRateActualAmount());
        }

        // 设置其他信息
        order.setSupplierId(enterpriseInfo.getEnterpriseId());
        order.setSupplierName(enterpriseInfo.getEnterpriseName());
        order.setFlishTime(new Date());
        order.setState(OrderEnum.STATE_FINISH.getCode());
        order.setOrderFreight(BigDecimal.ZERO);
        order.setProductType(product.getProductType());
        order.setOrderBillState(OrderEnum.ORDER_BILL_STATE_INIT.getCode());
        order.setOutKeyId(idStr);

        boolean saved = this.save(order);
        if (!saved) {
            throw new BusinessException("订单保存失败！");
        }

        return order;
    }

    /**
     * 设置订单类型和供应商信息
     */
    private void setOrderClassAndSupplier(Orders order, Shop shop, List<ProductBuyInfoDTO> dtos) {
        if (shop.getShopClass() == 2) {
            order.setOrderClass(2);
        } else {
            Product product = productService.getProductById(dtos.get(0).getProductId(), null);
            if (product != null) {
                order.setOrderClass(1);
                order.setSupplierId(product.getSupperBy());
                order.setSupplierName(product.getSupplierName());
            }
        }
    }

    /**
     * 创建订单明细
     */
    private List<OrderItem> createOrderItems(List<ProductBuyInfoDTO> dtos, Orders mainOrder) {
        List<OrderItem> orderItems = new ArrayList<>();

        for (ProductBuyInfoDTO dto : dtos) {
            Product product = productService.getProductById(dto.getProductId(), null);
            ProductSku productSku = getProductSku(dto.getProductId());

            OrderItem orderItem = buildOrderItem(dto, product, productSku, mainOrder);
            // 修改数据
            orderItemService.save(orderItem);

            if (mainOrder.getOrderClass() == 2) {
                orderItem.setCostPriceSum(
                        productSku.getCostPrice().multiply(dto.getCartNum()).setScale(2, RoundingMode.HALF_UP));
                orderItems.add(orderItem);
            }
        }

        return orderItems;
    }

    /**
     * 构建订单明细项
     */
    private OrderItem buildOrderItem(ProductBuyInfoDTO dto, Product product, ProductSku productSku,
            Orders order) {
        BigDecimal taxRate = dto.getTaxRate();
        OrderItem orderItem = new OrderItem();

        // 设置基本信息
        orderItem.setOrderId(order.getOrderId());
        orderItem.setOrderSn(order.getOrderSn());
        orderItem.setProductId(product.getProductId());
        orderItem.setPaymentPeriod(dto.getPaymentPeriod());
        orderItem.setProductSn(product.getSerialNum());
        orderItem.setProductName(product.getProductName().trim());
        orderItem.setProductImg(productSku.getSkuImg());
        orderItem.setSkuId(productSku.getSkuId());
        orderItem.setSkuName(productSku.getSkuName() == null ? null : productSku.getSkuName().trim());
        orderItem.setUnit(productSku.getUnit());

        // 设置价格信息
        orderItem.setProductPrice(dto.getSellPrice());
        orderItem.setBuyCounts(dto.getCartNum());
        orderItem.setTotalAmount(dto.getSellPrice().multiply(dto.getCartNum()).setScale(2, RoundingMode.HALF_UP));
        orderItem.setCostPrice(productSku.getCostPrice());
        orderItem.setOriginalPrice(productSku.getOriginalPrice());
        orderItem.setCostAmount(productSku.getCostPrice().multiply(dto.getCartNum()).setScale(2, RoundingMode.HALF_UP));

        if (productSku.getCostPrice() != null) {
            orderItem.setProfitPrice(dto.getSellPrice().subtract(productSku.getCostPrice()));
        }

        // 设置税率信息
        orderItem.setTaxRate(taxRate);
        BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(dto.getSellPrice(), taxRate);
        orderItem.setNoRatePrice(noRatePrice);
        BigDecimal noRateAmount = TaxCalculator.noTarRateItemAmount(orderItem.getTotalAmount(), noRatePrice,
                dto.getCartNum(), taxRate);
        orderItem.setNoRateAmount(noRateAmount);

        // 设置关联信息
        orderItem.setRelevanceName(product.getRelevanceName().trim());
        orderItem.setRelevanceNo(product.getRelevanceNo());
        orderItem.setRelevanceId(product.getRelevanceId());
        orderItem.setSupplierId(product.getSupperBy());
        orderItem.setClassId(product.getClassId());
        orderItem.setBrandId(product.getBrandId());
        orderItem.setBrandName(product.getBrandName());

        // 设置分类路径
        setOrderItemClassPath(orderItem, product.getClassId());

        // 设置其他信息
        orderItem.setProductType(product.getProductType());
        orderItem.setIsComment(OrderEnum.ORDER_ITEM_NOT_COMMENT.getCode());

        if (order.getOrderClass() == 2) {
            orderItem.setState(1);
        }

        return orderItem;
    }

    /**
     * 设置订单明细的分类路径
     */
    private void setOrderItemClassPath(OrderItem orderItem, String classId) {
        List<CategoryClassIdAndClassNameVO> categoryPath = productCategoryService.getCategoryParentPath(classId);

        if (CollectionUtils.isEmpty(categoryPath)) {
            throw new BusinessException("分类不存在！");
        }

        if (categoryPath.size() == 1) {
            orderItem.setClassPathName(categoryPath.get(0).getClassName());
            orderItem.setClassPathId(categoryPath.get(0).getClassId());
        } else {
            StringBuilder classPath = new StringBuilder();
            StringBuilder classPathId = new StringBuilder();

            for (CategoryClassIdAndClassNameVO category : categoryPath) {
                if (!StringUtils.isEmpty(category.getClassName())) {
                    if (classPath.length() > 0) {
                        classPath.append("/");
                        classPathId.append("/");
                    }
                    classPath.append(category.getClassName());
                    classPathId.append(category.getClassId());
                }
            }

            orderItem.setClassPathName(classPath.toString());
            orderItem.setClassPathId(classPathId.toString());
        }
    }

    /**
     * 处理库存扣减
     */
    // private void processInventoryDeduction(List<ProductBuyInfoDTO> dtos, List<ProductSku> productSkus,
    //         List<String> outProductIds) {
    //     // 修改库存
    //     if (!CollectionUtils.isEmpty(productSkus)) {
    //         // 修改数据
    //         productSkuService.updateBatchById(productSkus);
    //     }

    //     // 下架库存为0的商品
    //     if (!CollectionUtils.isEmpty(outProductIds)) {
    //         UpdateProductStateDTO updateProductStateDTO = new UpdateProductStateDTO();
    //         updateProductStateDTO.setState(2);
    //         updateProductStateDTO.setProductIds(outProductIds);
    //         // 修改数据
    //         productService.updateProductState(updateProductStateDTO);
    //     }
    // }

    /**
     * 处理订单拆分（多供方订单）
     */
    private void processOrderSplitting(Orders mainOrder, List<OrderItem> orderItems, BigDecimal mainTaxRate) {
        Map<String, List<OrderItem>> supplierOrderItems = orderItems.stream()
                .collect(Collectors.groupingBy(OrderItem::getSupplierId));

        for (Map.Entry<String, List<OrderItem>> entry : supplierOrderItems.entrySet()) {
            String supplierId = entry.getKey();
            List<OrderItem> items = entry.getValue();

            // 创建子订单
            Orders subOrder = createSubOrder(mainOrder, supplierId, items);

            // 更新订单明细
            updateOrderItemsForSubOrder(subOrder, items);
        }
    }

    /**
     * 创建子订单
     */
    private Orders createSubOrder(Orders mainOrder, String supplierId, List<OrderItem> orderItems) {
        // 获取供应商信息
        EnterpriseInfo supplierInfo = enterpriseInfoService.lambdaQuery()
                .eq(EnterpriseInfo::getEnterpriseId, supplierId)
                .select(EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getTaxRate)
                .one();

        if (supplierInfo == null) {
            throw new BusinessException("供应商不存在！");
        }

        BigDecimal taxRate = supplierInfo.getTaxRate();
        if (taxRate == null || taxRate.compareTo(BigDecimal.ZERO) == 0) {
            throw new BusinessException("【" + supplierInfo.getEnterpriseName() + "】供应商未设置税率！");
        }

        // 计算子订单金额
        BigDecimal totalCostPrice = BigDecimal.ZERO;
        BigDecimal noRateAmount = BigDecimal.ZERO;
        StringBuilder productNames = new StringBuilder();

        for (OrderItem item : orderItems) {
            BigDecimal itemCostTotal = item.getCostPrice().multiply(item.getBuyCounts()).setScale(2,
                    RoundingMode.HALF_UP);
            totalCostPrice = totalCostPrice.add(itemCostTotal);

            BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(item.getCostPrice(), taxRate);
            BigDecimal itemNoRateAmount = TaxCalculator.noTarRateItemAmount(itemCostTotal, noRatePrice,
                    item.getBuyCounts(), taxRate);
            noRateAmount = noRateAmount.add(itemNoRateAmount);

            if (productNames.length() > 0) {
                productNames.append(",");
            }
            productNames.append(item.getProductName());
        }

        // 创建子订单
        Orders subOrder = new Orders();
        BeanUtils.copyProperties(mainOrder, subOrder);

        subOrder.setOrderId(null);
        subOrder.setOrderSn(OrderUtils.getOrder());
        subOrder.setUntitled(productNames.toString());
        subOrder.setSupplierId(supplierId);
        subOrder.setSupplierName(supplierInfo.getEnterpriseName());
        subOrder.setTaxRate(taxRate);
        subOrder.setActualAmount(totalCostPrice);
        subOrder.setTotalAmount(BigDecimal.ZERO);
        subOrder.setCostPriceTotal(BigDecimal.ZERO);
        subOrder.setOrderClass(3);
        subOrder.setParentOrderId(mainOrder.getOrderId());
        subOrder.setOrderSourceType(1);
        subOrder.setOrderSourceId(mainOrder.getOrderId());

        if (mallConfig.isNotRateAmount == 1) {
            subOrder.setNoRateAmount(noRateAmount);
        } else {
            subOrder.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(totalCostPrice, taxRate));
        }

        boolean saved = this.save(subOrder);
        if (!saved) {
            throw new BusinessException("子订单保存失败！");
        }

        return subOrder;
    }

    /**
     * 更新子订单的订单明细
     */
    private void updateOrderItemsForSubOrder(Orders subOrder, List<OrderItem> orderItems) {
        BigDecimal taxRate = subOrder.getTaxRate();

        for (OrderItem item : orderItems) {
            // 保存父明细ID
            item.setParentOrderItemId(item.getOrderItemId());
            item.setOrderItemId(null);
            item.setOrderId(subOrder.getOrderId());
            item.setOrderSn(subOrder.getOrderSn());

            // 更新价格信息（子订单使用成本价）
            item.setProductPrice(item.getCostPrice());
            item.setTotalAmount(item.getCostPrice().multiply(item.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));

            // 更新税率信息
            item.setTaxRate(taxRate);
            BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(item.getCostPrice(), taxRate).setScale(2,
                    RoundingMode.HALF_UP);
            item.setNoRatePrice(noRatePrice);
            BigDecimal noRateAmount = TaxCalculator.noTarRateItemAmount(item.getTotalAmount(), noRatePrice,
                    item.getBuyCounts(), taxRate);
            item.setNoRateAmount(noRateAmount);

            // 清空成本相关字段
            item.setCostPrice(BigDecimal.ZERO);
            item.setCostAmount(BigDecimal.ZERO);
            item.setOriginalPrice(BigDecimal.ZERO);
        }

        // 修改数据
        orderItemService.saveBatch(orderItems);
    }

    /**
     * 创建计划订单关联信息
     */
    private List<OrderSelectPlan> createOrderSelectPlans(List<ProductBuyInfoDTO> dtos, Orders mainOrder,
            List<OrderItem> orderItems) {
        List<OrderSelectPlan> orderSelectPlans = new ArrayList<>();

        for (int i = 0; i < dtos.size(); i++) {
            ProductBuyInfoDTO dto = dtos.get(i);
            OrderItem orderItem = findOrderItemByProductId(orderItems, dto.getProductId());

            OrderSelectPlan plan = new OrderSelectPlan();
            plan.setOrderId(mainOrder.getOrderId());
            plan.setOrderSn(mainOrder.getOrderSn());
            plan.setOrderItemId(orderItem != null ? orderItem.getOrderItemId() : null);
            plan.setDtlId(dto.getDtlId());
            plan.setBillId(dto.getBillId());
            plan.setBillNo(dto.getBillNo());
            plan.setEquipmentName(getProductNameByProductId(dto.getProductId()));
            plan.setCount(dto.getCartNum());
            plan.setPrice(dto.getPrice());
            plan.setAccount(dto.getPrice().multiply(dto.getCartNum()).setScale(2, RoundingMode.HALF_UP));
            plan.setStorageId(dto.getStorageId());
            plan.setStorageName(dto.getStorageName());
            plan.setShortCode(dto.getShortCode());
            plan.setCreditCode(dto.getCreditCode());
            plan.setStorageOrgId(dto.getStorageOrgId());
            plan.setProductType(mainOrder.getProductType());
            plan.setPlanType(mainOrder.getProductType());
            plan.setTaxRate(mainOrder.getTaxRate());
            plan.setOrgId(ThreadLocalUtil.getCurrentUser().getOrgId());
            plan.setOrgName(ThreadLocalUtil.getCurrentUser().getEnterpriseName());

            // 设置供应商类型
            plan.setSupplierType(StringUtils.isEmpty(dto.getCreditCode()) ? 2 : 1);

            orderSelectPlans.add(plan);
        }

        return orderSelectPlans;
    }

    /**
     * 根据商品ID查找对应的订单明细
     */
    private OrderItem findOrderItemByProductId(List<OrderItem> orderItems, String productId) {
        return orderItems.stream()
                .filter(item -> productId.equals(item.getProductId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据商品ID获取商品名称
     */
    private String getProductNameByProductId(String productId) {
        Product product = productService.getProductById(productId, null);
        return product != null ? product.getProductName() : "";
    }

    private List<UpdatePlanDtl> getUpdatePlanDtl(List<ProductBuyInfoDTO> dtos, String pBillId, List<PlanDetail> planDetails) {
        List<UpdatePlanDtl> updatePlanDtls = new ArrayList<>(dtos.size());
        for (ProductBuyInfoDTO dto : dtos) {
            BigDecimal taxRate = dto.getTaxRate();
            BigDecimal totalAmount = dto.getSellPrice().multiply(dto.getCartNum()).setScale(2, RoundingMode.HALF_UP);
            BigDecimal noRateAmount = TaxCalculator.calculateNotTarRateAmount(totalAmount, taxRate);
            PlanDetail planDetail = planDetails.stream().filter(p->p.getBillId().equals(dto.getBillId())).findAny().get();
            UpdatePlanDtl updatePlanDtl = UpdatePlanDtl.builder().amount(noRateAmount)
                                    .number(dto.getCartNum())
                                    .dtlId(pBillId)
                                    .billId(planDetail.getPDtlId())
                                    .build();
            updatePlanDtls.add(updatePlanDtl);
        }
        return updatePlanDtls;
    }

    /**
     * 订单计算结果内部类
     */
    @Data
    private static class OrderCalculationResult {
        private BigDecimal actualAmount;
        private BigDecimal noRateActualAmount;
        private BigDecimal costPriceTotal;
        private String productNames;
        private HashSet<String> supplierIdSet;
        private List<ProductSku> productSkus;
        private List<String> outProductIds;
    }
}
