package scrbg.meplat.mall.vo.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2023-07-24 10:52
 */
@Data
public class GetBidingRecordItemVO {


    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "品牌名称")
    private String brand;

    @ApiModelProperty(value = "规格型号")
    private String spec;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "数量")
    private BigDecimal num;

    @ApiModelProperty(value = "商品材质")
    private String productTexture;





    @ApiModelProperty(value = "竞价记录明细id")
    private String bidRecordItemId;

    @ApiModelProperty(value = "竞价记录id")

    private String bidRecordId;

    @ApiModelProperty(value = "竞价采购商品id")

    private String biddingProductId;

    @ApiModelProperty(value = "竞价采购id")

    private String biddingId;

    @ApiModelProperty(value = "不含税到场单价")

    private BigDecimal bidPrice;


    @ApiModelProperty(value = "税率")

    private BigDecimal taxRate;


    @ApiModelProperty(value = "含税到场单价")

    private BigDecimal bidRatePrice;


    @ApiModelProperty(value = "含税总金额")

    private BigDecimal bidRateAmount;


    @ApiModelProperty(value = "不含税总金额")

    private BigDecimal bidAmount;

    @ApiModelProperty(value = "报价方备注")

    private String remarks;


    public BigDecimal getTaxRate() {
        if(taxRate != null) {
            return taxRate.divide(new BigDecimal(100));
        }else {
            return null;
        }
    }
}
