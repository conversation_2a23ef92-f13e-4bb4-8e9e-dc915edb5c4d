package scrbg.meplat.mall.pcwp.third.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 大宗零购计划分页查询条件类。
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BulkRetailPlanPageQueryCondition {
    private String beginPlanDate; // 计划日期(开始)
    private String creditCode;    // 信用代码
    private String endPlanDate;   // 计划日期(结束)
    private Boolean isChild;      // 是否获取下级机构
    private String keyword;       // 模糊查询关键字
    private Integer limit;        // 每页显示条数
    private String orgId;         // 单据机构
    private String orgShort;      // 供应商机构短码
    private Integer page;         // 当前页数
    private String sortFields;    // 排序方式，格式如： InDate DESC，可不填，保持默认排序即可
    private String supplierId;    // 供应商id
    private String supplierName;  // 供应商名称
}

