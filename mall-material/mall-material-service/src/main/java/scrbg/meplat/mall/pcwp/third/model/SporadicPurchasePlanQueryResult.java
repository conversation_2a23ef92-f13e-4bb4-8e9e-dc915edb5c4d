package scrbg.meplat.mall.pcwp.third.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 零星采购计划查询列表返回类。
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SporadicPurchasePlanQueryResult {
    private String billDate;   // 单据日期 (Swagger中为date-time格式，这里使用String兼容，也可考虑LocalDateTime)
    private String billId;     // 单据Id
    private String billNo;     // 单据编号
    private Integer month;     // 计划月份
    private String orgName;    // 机构名称
    private BigDecimal planAmount; // 计划金额
    private Integer state;     // 单据状态
    private Integer year;      // 计划年度
}
