package scrbg.meplat.mall.config.rabbitMQ;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.DependsOn;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

@Component
@Log4j2
@ConditionalOnProperty(name = "spring.rabbitmq.mdm.isSync", havingValue = "true", matchIfMissing = false)
@DependsOn("mdmRabbitMQConfig") // 关键注解
public class MdmRabbitMQListener {

    // 使用Map存储队列处理方法，避免长switch-case
    private final Map<String, Consumer<String>> queueHandlers = new HashMap<>();

    public MdmRabbitMQListener() {
        // 初始化队列处理器映射
        queueHandlers.put("Personinfo_Full_dev", this::processPersonInfo);
        queueHandlers.put("Zcinfo_Full_dev", this::processZcinfo);
        queueHandlers.put("Zyzginfo_Full_dev", this::processZyzginfo);
        queueHandlers.put("Grhjinfo_Full_dev", this::processGrhjinfo);
        queueHandlers.put("Rzjlinfo_Full_dev", this::processRzjlinfo);
        queueHandlers.put("Orginfo_Full_dev", this::processOrginfo);
        queueHandlers.put("Sys_Org_Full_dev", this::processSysOrginfo);
        queueHandlers.put("Sys_ConOrg_Full_dev", this::processSysConOrginfo);
        queueHandlers.put("Project_Project_Full_dev", this::processProjectinfo);
        queueHandlers.put("TTAccount_Full_dev", this::processTTAccountinfo);
    }
//    @RabbitListener(queues = "Personinfo_Full_dev",containerFactory = "rabbitListenerContainerFactory") // 明确指定工厂
    @RabbitListener(queues = "#{@mdmQueueNames}",containerFactory = "rabbitListenerContainerFactory") // 明确指定工厂
    public void handleAllMessages(Message message,
                                  Channel channel,
                                  @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                  @Header("amqp_consumerQueue") String queueName) {
        try {
            String messageBody = new String(message.getBody());
            log.info(message.getMessageProperties());
            if(true){
                return;
            }
            //log.info("开始处理队列[{}]消息，消息ID: {}", queueName, message.getMessageProperties().getMessageId());
            // 查找对应的处理器
            Consumer<String> handler = queueHandlers.get(queueName);
            if (handler == null) {
                log.error("未找到队列[{}]对应的处理器", queueName);
                channel.basicNack(deliveryTag, false, false); // 不重新入队
            } else {// 处理消息
                handler.accept(messageBody);// 确认消息处理完成
                //channel.basicAck(deliveryTag, false);
                log.info("队列[{}]消息处理完成", queueName);
            }
        }  catch (Exception e) {
            log.error("处理队列[{}]消息异常，消息将重新入队: {}", queueName, e.getMessage());
            try {
                channel.basicNack(deliveryTag, false, true); //
            } catch (IOException ioException) {
                log.error("拒绝消息失败: {}", ioException.getMessage());
            }
        }
    }

    private void processPersonInfo(String message) {
        try {
            JSONArray jsonArray = JSON.parseArray(message);
            if(jsonArray == null || jsonArray.isEmpty()){
                log.warn("人员基本信息队列收到空消息");
                return;
            }
            log.warn("人员基本信息队列收到空消息数量为"+jsonArray.size());
            for (int i = 0,count = jsonArray.size(); i < count; i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String id = jsonObject.getString("id");
                String pname = jsonObject.getString("pname");
                String pnumber = jsonObject.getString("pnumber");
                String orgnumber = jsonObject.getString("orgnumber");
                String gender = jsonObject.getString("gender");
                String deptnumber = jsonObject.getString("deptnumber");
                Integer age = jsonObject.getInteger("age");
                String idcard = jsonObject.getString("idcard");
                String gw = jsonObject.getString("gw");
                String xl = jsonObject.getString("xl");
                Float gznx = jsonObject.getFloat("gznx");
                String xz = jsonObject.getString("xz");
                String bysj = jsonObject.getString("bysj");
                String byyx = jsonObject.getString("byyx");
                String byzy = jsonObject.getString("byzy");
                String mobile = jsonObject.getString("mobile");
                String sbbm = jsonObject.getString("sbbm");
                String sbjndw = jsonObject.getString("sbjndw");
                String yx = jsonObject.getString("yx");
                String tc = jsonObject.getString("tc");
                String lastupdatedate = jsonObject.getString("lastupdatetime");
                String mdmstate = jsonObject.getString("MdmState");
                int a = 1;
                // 处理人员信息业务逻辑
                // personService.process(personInfo);
            }
        } catch (Exception e) {
            log.error("处理人员信息异常: {}", e.getMessage());
        }
    }

    private void processZcinfo(String message) {
        try {
            log.info("开始处理证书信息: {}", message);
            // 具体业务逻辑
        } catch (Exception e) {
            log.error("处理证书信息异常: {}", e.getMessage());
        }
    }

    private void processZyzginfo(String message) {
        try {
            log.info("开始处理执业资格信息: {}", message);
            // 具体业务逻辑
        } catch (Exception e) {
            log.error("处理执业资格信息异常: {}", e.getMessage());
        }
    }

    // 其他处理方法保持不变，但建议都添加类似的异常处理逻辑
    private void processGrhjinfo(String message) {
        log.info("processGrhjinfo");
    }

    private void processRzjlinfo(String message) {
        log.info("processRzjlinfo");
    }

    private void processOrginfo(String message) {
        log.info("processOrginfo");
    }

    private void processSysOrginfo(String message) {
        log.info("processSysOrginfo");
    }

    private void processSysConOrginfo(String message) {
        log.info("processSysConOrginfo");
    }

    private void processProjectinfo(String message) {
        log.info("processProjectinfo");
    }

    private void processTTAccountinfo(String message) {
        log.info("processTTAccountinfo");
    }

}