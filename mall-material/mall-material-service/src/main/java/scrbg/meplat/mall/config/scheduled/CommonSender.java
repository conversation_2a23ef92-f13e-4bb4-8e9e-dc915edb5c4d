package scrbg.meplat.mall.config.scheduled;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.SystemParam;
import scrbg.meplat.mall.service.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.baomidou.mybatisplus.core.toolkit.Wrappers.lambdaQuery;

/**
 * <AUTHOR>
 * @create 2022-12-09 13:55
 */
@Log4j2
@EnableScheduling //开启定时器功能
@Component
@ConditionalOnProperty(name="app.schedule.enable",havingValue = "true")
public class CommonSender {

    @Autowired
    private FileRecordDeleteService fileRecordDeleteService;

    @Autowired
    private MaterialReconciliationService materialReconciliationService;

    @Autowired
    private OrderShipService orderShipService;

    @Autowired
    MallConfig mallConfig;

    @Autowired
    private FileService fileService;

    @Autowired
    SystemParamService systemParamService;

    @Autowired
    private EnterpriseInfoService enterpriseInfoService;

    @Autowired
    PlatformDealFeeRecordService platformDealFeeRecordService;

    @Autowired
    ShopCommentService shopCommentService;

    @Autowired
    ProductService productService;

//    @Scheduled(cron = "0 0/1 * * * ?") // 每分钟执行
//    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
//    public void updateIndexParameterCount() {
//        if (mallConfig.mallType ==0) {
//            log.info("修改了统计数量！");
//            systemParamService.updateIndexParameterCount();
//        }
//    }

    /**
     * 修改企业是否加入pcwp库
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void upEnterPriseParameterIsPcwp() {
        log.info("修改企业是否加入pcwp库！");
        enterpriseInfoService.updateIspcwpState();
    }

    /**
     * 修改所有商品的销量
     */
    @Autowired
    private OrderItemService orderItemService;

    @Scheduled(cron = "0 0 3 * * ?")
    public void upProductSoldNum() {
        orderItemService.upProductSoldNum();
    }

    /**
     * 推送验收单到pcwp
     */
    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨2点执行
    public void pushMaterialReconciliationData() {
        materialReconciliationService.pushMaterialReconciliationData();
    }

    /**
     * 推送发货单到pcwp
     */
    @Autowired
    PlatformYearFeeService platformYearFeeService;

    /**
     * 店铺年费过期提醒，冻结店铺和商品不展示
     */
    @Scheduled(cron = "0 1 0 * * ?")
//    @Scheduled(cron = "0 0/1 * * * ?")
    public void platformYearFeeService() {
        platformYearFeeService.updateYearFeeOutTime();
    }

    /**
     * 店铺年费到期前提示，每日凌晨1点查找店铺年费30天到期
     */
    @Scheduled(cron = "0 1 * * * ?")
//    @Scheduled(cron = "0 0/1 * * * ?")
    public void checkYearFeeExpirationNotification() {
        platformYearFeeService.yearFeeExpirationReminder();
    }

    /**
     * 交易服务费截止日期完成缴费提醒
     * 每天凌晨1点执行
     */
    @Scheduled(cron = "0 1 * * * ?")
    public void executeQuarterlySettlementNotification() {
        platformDealFeeRecordService.reminderForPaymentDueDate();
    }

    /**
     * 季度交易服务费结算任务
     * 每天凌晨1点执行
     */
//    @Scheduled(cron = "0 0/1 * * * ?")
    @Scheduled(cron = "0 1 * * * ?")
    public void executeQuarterlySettlement() {
        try {
            // 获取当前日期
            LocalDate today = LocalDate.now();
            String currentMonthDay = today.format(DateTimeFormatter.ofPattern("MM-dd"));
            // 获取所有参数，不限制数量
            List<SystemParam> allParams = systemParamService.listAllParams();
            // 使用流处理数据
            Map<String, String> paramMap = allParams.stream()
                    .filter(param -> param != null && param.getCode() != null && param.getKeyValue() != null)
                    .collect(Collectors.toMap(
                            SystemParam::getCode,
                            SystemParam::getKeyValue,
                            (v1, v2) -> v1
                    ));
            // 提取四个季度结算日期的值
            String dateQ1 = paramMap.get("settlementDateQ1"); // 应为 "03-20"
            String dateQ2 = paramMap.get("settlementDateQ2"); // 应为 "06-20"
            String dateQ3 = paramMap.get("settlementDateQ3"); // 应为 "09-20"
            String dateQ4 = paramMap.get("settlementDateQ4"); // 应为 "12-20"
            // 检查当前日期是否是某个季度的结算日期
            String quarterCode = null;
            LocalDate quarterStartDate = null;
            LocalDate quarterEndDate = null;
            int year = today.getYear();

            if (dateQ1 != null && currentMonthDay.equals(dateQ1)) {
                quarterCode = "Q1";//"第一季度"  第一季度结算日: 3月20日 (上一年12月20日至本年3月19日)
                quarterStartDate = LocalDate.of(year - 1, 12, 20);
                quarterEndDate = LocalDate.of(year, 3, 19);
            } else if (dateQ2 != null && currentMonthDay.equals(dateQ2)) {
                quarterCode = "Q2";//"第二季度"  第二季度结算日: 6月20日 (本年3月20日至6月19日)
                quarterStartDate = LocalDate.of(year, 3, 20);
                quarterEndDate = LocalDate.of(year, 6, 19);
            } else if (dateQ3 != null && currentMonthDay.equals(dateQ3)) {
                quarterCode = "Q3";//"第三季度"  第三季度结算日: 9月20日 (本年6月20日至9月19日)
                quarterStartDate = LocalDate.of(year, 6, 20);
                quarterEndDate = LocalDate.of(year, 9, 19);
            } else if (dateQ4 != null && currentMonthDay.equals(dateQ4)) {
                quarterCode = "Q4";//第四季度  第四季度结算日: 12月20日 (本年9月20日至12月19日)
                quarterStartDate = LocalDate.of(year, 9, 20);
                quarterEndDate = LocalDate.of(year, 12, 19);
            }
            if (quarterCode == null) {// 如果不是结算日，直接返回
                log.info("当前日期 {} 不是配置的季度结算日期，跳过结算任务", today);
                return;
            }
            //生成交易服务费结算单
            platformDealFeeRecordService.generateQuarterlyServiceFee(quarterStartDate, quarterEndDate);
        } catch (Exception e) {
            log.error("交易服务费结算定时任务执行异常", e);
            throw e;
        }
    }
//    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
//    public void deleteFile(){
//        // TODO 查询file数据库是否有在使用，未使用才调用远程进行删除   用户、企业、楼层
//        List<FileRecordDelete> list = fileRecordDeleteService.list();
//        for (FileRecordDelete fileRecordDelete : list) {
//            Integer count = fileService.lambdaQuery().eq(File::getFileId, fileRecordDelete.getRecordId()).count();
//            if(count > 0) {
//                continue;
//            }
//            if(mallConfig.mallType == 1){
//                Integer count1 = enterpriseInfoService.lambdaQuery().and(t -> {
//                    t.eq(EnterpriseInfo::getBusinessLicenseId, fileRecordDelete.getRecordId())
//                            .or()
//                            .eq(EnterpriseInfo::getCardPortraitFaceId, fileRecordDelete.getRecordId())
//                            .or()
//                            .eq(EnterpriseInfo::getCardPortraitNationalEmblemId, fileRecordDelete.getRecordId());
//                }).count();
//                if(count1 > 0){
//                    continue;
//                }
//            }
//            R r = iossFengin.deleteByRecordId(fileRecordDelete.getRecordId());
//            if(r.getCode() == 200){
//                fileRecordDeleteService.delete(fileRecordDelete.getFileRecordId());
//            }
//            System.out.println("--------------------------删除了文件------------------------");
//        }
//    }
    /**
     * 计算店铺评价综合评分
     */
    @Scheduled(cron = "0 0 3 * * ?") // 凌晨3点执行
    public void shopCommentCalculate() {
        log.info("计算商铺服务评分");
        shopCommentService.shopCommentCalculate();
    }

    /**
     * 计算商品均价
     * 每季度首日0点执行
     */
    @Scheduled(cron = "0 0 0 1 1,4,7,10 *")
    public void calculateProductAveragePrice() {
        productService.calculateProductAveragePrice();
    }



}
