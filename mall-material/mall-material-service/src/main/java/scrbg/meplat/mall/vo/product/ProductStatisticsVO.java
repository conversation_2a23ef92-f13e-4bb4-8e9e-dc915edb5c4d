package scrbg.meplat.mall.vo.product;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ProductStatisticsVO {
    private String shopId;
    private Integer productType;
    private String supplierName;
    private Integer onShelfCount;
    private Integer offShelfCount;
    private Integer rejectedCount;
    private Integer totalOnAndOffShelfCount;
    private Date createTime;
    @ApiModelProperty(value = "图标题（保存在数组第一个）")
    @TableField(exist = false)
    List<String> labelTitle;

    @ApiModelProperty(value = "图数量（保存在数组第一个）")
    @TableField(exist = false)
    List<Integer> count;
}