package scrbg.meplat.mall.config.rabbitMQ;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

/**
 * RabbitMQ配置
 */
@Component
@Slf4j
public class TTRabbitMQConfig {

    @Value("${spring.rabbitmq.tt.host:**************}")
    private String host;

    @Value("${spring.rabbitmq.tt.port:5672}")
    private Integer port;

    @Value("${spring.rabbitmq.tt.username:wzsc}")
    private String username;

    @Value("${spring.rabbitmq.tt.password:FABF26F6-7615-EB92-E158-09F3125DC089}")
    private String password;

    @Value("${spring.rabbitmq.tt.virtual-host:ToDoList}")
    private String virtualHost;


    @Value("${spring.rabbitmq.sm.host}")
    private String host_sm;

    @Value("${spring.rabbitmq.sm.port}")
    private Integer port_sm;

    @Value("${spring.rabbitmq.sm.username}")
    private String username_sm;

    @Value("${spring.rabbitmq.sm.password}")
    private String password_sm;

    @Value("${spring.rabbitmq.sm.virtual-host}")
    private String virtualHost_sm;


    @Value("${spring.rabbitmq.mdm.host}")
    private String host_mdm;

    @Value("${spring.rabbitmq.mdm.port}")
    private Integer port_mdm;

    @Value("${spring.rabbitmq.mdm.username}")
    private String username_mdm;

    @Value("${spring.rabbitmq.mdm.password}")
    private String password_mdm;

    @Value("${spring.rabbitmq.mdm.virtual-host}")
    private String virtualHost_mdm;

    @Bean
    public org.springframework.amqp.rabbit.connection.CachingConnectionFactory ttConnectionFactory() {
        org.springframework.amqp.rabbit.connection.CachingConnectionFactory factory =
                new org.springframework.amqp.rabbit.connection.CachingConnectionFactory();
        factory.setHost(host);
        factory.setPort(port);
        factory.setUsername(username);
        factory.setPassword(password);
        factory.setVirtualHost(virtualHost);

        log.info("TT RabbitMQ配置: {}:{}/{}", host, port, virtualHost);
        return factory;
    }


    @Bean
    public RabbitTemplate ttRabbitTemplate() {
        return new RabbitTemplate(ttConnectionFactory());
    }

    @Bean
    @Primary
    public RabbitTemplate RabbitTemplate() {
        org.springframework.amqp.rabbit.connection.CachingConnectionFactory factory =
                new org.springframework.amqp.rabbit.connection.CachingConnectionFactory();
        factory.setHost(host_sm);
        factory.setPort(port_sm);
        factory.setUsername(username_sm);
        factory.setPassword(password_sm);
        factory.setVirtualHost(virtualHost_sm);
        return new RabbitTemplate(factory);
    }

    @Bean
    public RabbitTemplate mdmRabbitTemplate() {
        org.springframework.amqp.rabbit.connection.CachingConnectionFactory factory =
                new org.springframework.amqp.rabbit.connection.CachingConnectionFactory();
        factory.setHost(host_mdm);
        factory.setPort(port_mdm);
        factory.setUsername(username_mdm);
        factory.setPassword(password_mdm);
        factory.setVirtualHost(virtualHost_mdm);
        return new RabbitTemplate(factory);
    }
}
