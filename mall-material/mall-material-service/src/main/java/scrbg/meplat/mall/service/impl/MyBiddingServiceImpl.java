package scrbg.meplat.mall.service.impl;/**
 * <AUTHOR>
 * @date 2023/7/20
 */

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.logging.Log;
import org.apache.ibatis.logging.LogFactory;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.bidding.BidOfferInfoVo;
import scrbg.meplat.mall.dto.bidding.BidOfferVo;
import scrbg.meplat.mall.dto.bidding.BidRowInfo;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.*;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.*;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
import scrbg.meplat.mall.vo.bidding.MyBiddingVo;
import scrbg.meplat.mall.vo.platform.ListShipByAffirmListVO;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * @program: maill_api
 * @description: 我参与的竞价
 * @author: 代文翰
 * @create: 2023-07-20 00:28
 **/
@Service
public class MyBiddingServiceImpl implements MyBiddingService {
    protected Log log = LogFactory.getLog(getClass());

    @Autowired
    private MyBiddingMapper baseMapper;

    @Autowired
    private MallConfig mallConfig;
    @Autowired
    private BiddingSuppliersService suppliersService;
    @Autowired
    private BiddingPurchaseService purchaseService;
    @Autowired
    private BiddingBidRecordService recordService;
    @Autowired
    private BiddingBidRecordItemService recordItemService;
    @Autowired
    private BiddingProductService biddingProductService;
    @Autowired
    private FileService fileService;
    @Resource
    private EnterpriseInfoService enterpriseInfoService;

    @Override
    public PageUtils queryMyBidList(JSONObject jsonObject) {

        Map<String, Object> innerMap = jsonObject.getInnerMap();
        MyBiddingVo myBiddingVo = new MyBiddingVo();
        List timeRange = (List) innerMap.get("timeRange");
        if (timeRange != null) {
            if (timeRange.size() > 1) {
                myBiddingVo.setStartTime(timeRange.get(0).toString());
                myBiddingVo.setEndTime(timeRange.get(1).toString());
            }

        }

        String keywords = (String) innerMap.get("keywords");
        String title = (String) innerMap.get("title");
        if (StringUtils.isNotBlank(title)) {
            myBiddingVo.setTitle(title);
        }
        //Integer type = (Integer) innerMap.get("biddingType");
        //if (type != null) {
        //    myBiddingVo.setType(type);
        //}
        // 竞价状态
        Integer state = (Integer) innerMap.get("state");
        if (state != null) {
            myBiddingVo.setBidState(state);
        }
        Integer type = (Integer) innerMap.get("type");
        if (type != null) {
            myBiddingVo.setType(type);
        }
        Integer publicState = (Integer) innerMap.get("publicState");
        if (publicState != null) {
            myBiddingVo.setPublicityState(publicState);
        }
        Integer orderBy = (Integer) innerMap.get("orderBy");
        if (orderBy != null) {
            myBiddingVo.setOrderBy(orderBy);
        }
        Integer productType = (Integer) innerMap.get("productType");
        if (productType != null) {
            myBiddingVo.setProductType(productType);
        }
        //  条件查询

        if (StringUtils.isNotBlank(keywords)) {
            myBiddingVo.setKeyWords(keywords);
        }
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        // 根据当前用户的机构ID（供应商ID）查询
        myBiddingVo.setSupplierId(user.getEnterpriseId());
        int count = baseMapper.getMyBidListCount(myBiddingVo);
        pageUtils.pageDispose(jsonObject, count);
        Page<MyBiddingVo> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<MyBiddingVo> bidList = baseMapper.getMyBidList(pages, myBiddingVo);
        pages.setRecords(bidList);
        return new PageUtils(pages);

    }

    @Override
    public BidOfferVo queryMyOfferPrice(String bidSn) {
        // 竞价采购ID
        BidOfferVo offerVo = getBidOfferVoByBidSn(bidSn);
        return offerVo;
    }

    /***
     * 根据竞价编号查询商品数据
     * @param bidSn
     * @return
     */
    private BidOfferVo getProductsByBidSn(String bidSn) {
        if (StringUtils.isBlank(bidSn)) {
            throw new BusinessException("竞价编号为空");
        }
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, enterpriseId).one();
        BidOfferVo bidOfferVo = new BidOfferVo();
        List<BiddingProduct> biddingProductList = biddingProductService.lambdaQuery().eq(BiddingProduct::getBiddingSn, bidSn)
                //.eq(BiddingProduct::getCreateOrgId,enterpriseId)
                .list();
        BiddingPurchase purchase = purchaseService.lambdaQuery().eq(BiddingPurchase::getBiddingSn, bidSn).one();
        String title = purchase.getTitle();
        bidOfferVo.setTitle(title);
        String biddingSn = purchase.getBiddingSn();
        bidOfferVo.setBiddingSn(biddingSn);
        Integer publicityState = purchase.getPublicityState();
        bidOfferVo.setPublicityState(publicityState);
        String orgName = purchase.getCreateOrgName();
        bidOfferVo.setCreateOrgName(orgName);
        Date startTime = purchase.getStartTime();
        bidOfferVo.setStartTime(startTime);
        Date endTime = purchase.getEndTime();
        bidOfferVo.setEndTime(endTime);
        bidOfferVo.setProductType(purchase.getProductType());
        bidOfferVo.setBidAmount(new BigDecimal(0));
        bidOfferVo.setBillType(purchase.getBillType());
        bidOfferVo.setTaxRate(enterpriseInfo.getTaxRate() == null ? new BigDecimal(0) : enterpriseInfo.getTaxRate());
        bidOfferVo.setBiddingNotice(purchase.getBiddingNotice());
        Date date = new Date();
        if (date.after(purchase.getEndTime())) {
            bidOfferVo.setEditAble(0);
        }
        bidOfferVo.setStatus(0);
        bidOfferVo.setBidRateAmount(new BigDecimal(0));
        for (BiddingProduct product : biddingProductList) {
            BidOfferInfoVo info = new BidOfferInfoVo();
            info.setBidPrice(new BigDecimal(0));
            info.setTaxRate(new BigDecimal(0));
            info.setBidRatePrice(new BigDecimal(0));
            info.setBidRateAmount(new BigDecimal(0));

            info.setDeliveryAddress(product.getDeliveryAddress());
            Date deliveryDate = product.getDeliveryDate();
            if (deliveryDate == null){
                info.setDeliveryDate(null);
            }else {
                info.setDeliveryDate(DateUtil.getyyymmdd(deliveryDate));
            }
            info.setUnit(product.getUnit());
            info.setProductName(product.getProductName());
            info.setSpec(product.getSpec());
            info.setBiddingProductId(product.getBiddingProductId());
            info.setProductTexture(product.getProductTexture());
            info.setNum(product.getNum().setScale(2, BigDecimal.ROUND_HALF_UP));
            // 限价
            info.setReferencePrice(product.getReferencePrice());
            // 默认值为零(浮动报价在竞价生成时候填写)
            if (product.getNetPrice()!=null && product.getNetPrice().compareTo(new BigDecimal(0)) > 0){
                info.setNetPrice(product.getNetPrice());
            }else {
                info.setNetPrice(new BigDecimal(0));
            }
            info.setFixationPrice(new BigDecimal(0));
            info.setOutFactoryPrice(new BigDecimal(0));
            info.setTransportPrice(new BigDecimal(0));
            bidOfferVo.getProductList().add(info);
        }
        return bidOfferVo;
    }

    private BidOfferVo getBidOfferVoByBidSn(String bidSn) {
        if (StringUtils.isBlank(bidSn)) {
            throw new BusinessException("竞价采购编号不可为空");
        }
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String enterpriseId = user.getEnterpriseId();
        BidOfferVo bidOfferVo = new BidOfferVo();
        // 当前竞价信息
        BiddingPurchase purchase = purchaseService.lambdaQuery()
                .eq(BiddingPurchase::getBiddingSn, bidSn).one();
        if (ObjectUtil.isEmpty(purchase)) {
            throw new BusinessException("竞价数据不存在");
        }
        String title = purchase.getTitle();
        bidOfferVo.setTitle(title);
        bidOfferVo.setProductType(purchase.getProductType());
        String biddingSn = purchase.getBiddingSn();
        bidOfferVo.setBiddingSn(biddingSn);
        Integer publicityState = purchase.getPublicityState();
        bidOfferVo.setPublicityState(publicityState);
        String orgName = purchase.getCreateOrgName();
        bidOfferVo.setCreateOrgName(orgName);
        Date startTime = purchase.getStartTime();
        bidOfferVo.setStartTime(startTime);
        Date endTime = purchase.getEndTime();
        bidOfferVo.setEndTime(endTime);
        bidOfferVo.setBillType(purchase.getBillType());
        bidOfferVo.setBiddingNotice(purchase.getBiddingNotice());
        // 获取标题等数据
        // 获取竞价记录 根据当前竞价ID和供应商ID
        BiddingBidRecord record = recordService.lambdaQuery()
                .eq(BiddingBidRecord::getBiddingSn, bidSn)
                .and(wrapper -> {
                    wrapper.eq(BiddingBidRecord::getSupplierId, user.getEnterpriseId());
                })
                .one();
        // 增加文件下载
        if (record != null) {
            List<File> bidFile = fileService.lambdaQuery().eq(File::getRelevanceId, record.getBidRecordId())
                    .eq(File::getRelevanceType, 15)
                    .eq(File::getFileType, 3).list();
            if (!CollectionUtils.isEmpty(bidFile)) {
                bidOfferVo.setBidFile(bidFile.get(0));
            }
        }

        // 获取竞价明细 一对多
        if (ObjectUtil.isEmpty(record)) {
            // 说明首次报价查询
            return getProductsByBidSn(bidSn);
        }
        bidOfferVo.setBidRecordId(record.getBidRecordId());
        // 竞价记录的总价和税总价
        Date date = new Date();
        if (date.after(purchase.getEndTime())) {
            bidOfferVo.setEditAble(0);
        }
        bidOfferVo.setStatus(record.getState());
        bidOfferVo.setBidAmount(record.getBidAmount());
        bidOfferVo.setBidRateAmount(record.getBidRateAmount());
        bidOfferVo.setTaxRate(record.getTaxRate());

        List<BiddingBidRecordItem> itemList = recordItemService.lambdaQuery()
                .eq(BiddingBidRecordItem::getBidRecordId, record.getBidRecordId()).list();
        //根据明细表的竞价采购商品ID 查询竞价商品表的商品数据
        for (BiddingBidRecordItem item : itemList) {
            BidOfferInfoVo info = new BidOfferInfoVo();
            // 组装竞价明细
            info.setBidPrice(item.getBidPrice());
            info.setRemarks(item.getRemarks());
            info.setTaxRate(item.getTaxRate());
            info.setBidRatePrice(item.getBidRatePrice());
            info.setBidRateAmount(item.getBidRateAmount());
            info.setBidAmount(item.getBidAmount());
            //info.setRemarks(item.getRemarks());
            String productId = item.getBiddingProductId();
            // 组装物资明细
            BiddingProduct product = biddingProductService.lambdaQuery()
                    .eq(BiddingProduct::getBiddingProductId, productId)
                    //.eq(BiddingProduct::getBiddingSn,biddingSn)
                    .one();
            info.setDeliveryAddress(product.getDeliveryAddress());
            Date deliveryDate = product.getDeliveryDate();
            if (deliveryDate == null){
                info.setDeliveryDate(null);
            }else {
                info.setDeliveryDate(DateUtil.getyyymmdd(deliveryDate));
            }
            info.setUnit(product.getUnit());
            info.setProductName(product.getProductName());
            info.setSpec(product.getSpec());
            info.setBiddingProductId(product.getBiddingProductId());
            info.setProductTexture(product.getProductTexture());
            info.setNum(product.getNum());
            // 限价
            info.setReferencePrice(product.getReferencePrice());
            // 大宗商品
            if (purchase.getProductType() == 1 || purchase.getProductType() == 2){
                if (purchase.getBillType() == 1){
                     // 浮动
                    info.setNetPrice(item.getNetPrice());
                    info.setFixationPrice(item.getFixationPrice());
                }if (purchase.getBillType() == 2){
                    // 固定
                    info.setOutFactoryPrice(item.getOutFactoryPrice());
                    info.setTransportPrice(item.getTransportPrice());
                }
            }
            bidOfferVo.getProductList().add(info);
        }

        return bidOfferVo;
    }

    @Override
    @NotResubmit
    @Transactional(rollbackFor = Exception.class)
    public void udateMyOfferPrice(JSONObject jsonObject, String biddingSn) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        // 当前日期超过了截止时间则不允许修改/已经提交不允许修改
        BiddingBidRecord biddingBidRecord = recordService.lambdaQuery()
                .eq(BiddingBidRecord::getBiddingSn, biddingSn)
                .eq(BiddingBidRecord::getSupplierId, user.getEnterpriseId())
                .one();
        String taxRateString = jsonObject.getString("taxRate");
        if (StringUtils.isBlank(taxRateString)){
            throw new BusinessException("请设置税率");
        }
        List jsonArray =  ((List) jsonObject.get("tableData"));
        BigDecimal taxRate = new BigDecimal(taxRateString).setScale(2,BigDecimal.ROUND_HALF_UP);
        if (biddingBidRecord == null) {
            BiddingPurchase purchase = purchaseService.lambdaQuery().eq(BiddingPurchase::getBiddingSn, biddingSn).one();
            Date date = new Date();
            if (date.after(purchase.getEndTime())) {
                throw new BusinessException("已经超过截止时间，无法提交报价");
            }
            // 报价插入明细表
            // recordService
            //recordItemService
            List<BiddingBidRecordItem> recordItemList = new ArrayList<>();
            BigDecimal bidRecordAmount = new BigDecimal(0);
            BigDecimal bidRecordRateAmount = new BigDecimal(0);
            for (Object o : jsonArray) {
                // 一条商品数据
                JSONObject object = JSONObject.parseObject(JSONObject.toJSONString(o));
                Map<String, Object> map = object.getInnerMap();
                String biddingProductId = (String) map.get("biddingProductId");
                String num = String.valueOf(map.get("num"));
                BigDecimal bigNumDecimal = new BigDecimal(num);
                String bidRatePriceStr = String.valueOf(map.get("bidRatePrice"));
                String remarks = String.valueOf(map.get("remarks"));
                BigDecimal bidRatePriceDecimal = new BigDecimal(bidRatePriceStr).setScale(2, BigDecimal.ROUND_HALF_UP);
                // 不含税到场单价
                BigDecimal bidPriceDecimal = TaxCalculator.calculateNotTarRateAmount(bidRatePriceDecimal, taxRate);
                // 限价
                BiddingProduct p = biddingProductService.lambdaQuery().eq(BiddingProduct::getBiddingProductId, biddingProductId).one();
                if (p.getReferencePrice() != null) {
                    // 含税到场大雨参考价异常
                    if (bidRatePriceDecimal.compareTo(p.getReferencePrice()) > 0 && p.getReferencePrice().compareTo(new BigDecimal(0)) > 0) {
                        throw new BusinessException(p.getProductName() + "含税单价大于参考单价");
                    }
                }
                // 结束
                // 含税金额 = 数量 X  含税到场单价
                BigDecimal bidRateAmount = bidRatePriceDecimal.multiply(bigNumDecimal).setScale(2, BigDecimal.ROUND_HALF_UP);
                bidRecordRateAmount = bidRecordRateAmount.add(bidRateAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                // 不含税总金额
                BigDecimal bidAmount = TaxCalculator.calculateNotTarRateAmount(bidRateAmount,taxRate);
                BiddingBidRecordItem item = new BiddingBidRecordItem();
                item.setBiddingProductId(biddingProductId);
                item.setBiddingId(purchase.getBiddingId());
                item.setBidPrice(bidPriceDecimal.setScale(2, BigDecimal.ROUND_HALF_UP));
                item.setBidAmount(bidAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                item.setBidRateAmount(bidRateAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
                item.setTaxRate(taxRate);
                if (StringUtils.isNotBlank(remarks)){
                    item.setRemarks(remarks);
                }
                item.setBidRatePrice(bidRatePriceDecimal);
                recordItemList.add(item);
                //boolean re = recordItemService.lambdaUpdate().eq(BiddingBidRecordItem::getBiddingProductId, biddingProductId).update(item);
                //if (!re) {
                //    throw new BusinessException("报价明细失败");
                //}
            }
            // 计算总的不含税总金额
            bidRecordAmount = TaxCalculator.calculateNotTarRateAmount(bidRecordRateAmount, taxRate);
            BiddingBidRecord record = new BiddingBidRecord();
            //recordService.lambdaUpdate()
            record.setBiddingId(purchase.getBiddingId());
            record.setTaxRate(taxRate);
            record.setBidRecordSn(CodeGenerator.generateUniqueCode());
            record.setBidAmount(bidRecordAmount);
            record.setBidRateAmount(bidRecordRateAmount);
            record.setBiddingSn(biddingSn);
            UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
            record.setSupplierId(currentUser.getEnterpriseId());
            record.setSupplierName(currentUser.getEnterpriseName());
            record.setBidTime(new Date());
            // 状态(0默认1已提交5中标待审核6中标审核通过7审核失败)
            record.setState(0);
            recordService.create(record);
            BiddingBidRecord newRecord = recordService.lambdaQuery().eq(BiddingBidRecord::getBiddingSn, biddingSn)
                    .eq(BiddingBidRecord::getSupplierId, user.getEnterpriseId())
                    .one();
            //recordItemList.stream().forEach(latest->{
            //    latest.setBidRecordId(newRecord.getBidRecordId());
            //});
            for (BiddingBidRecordItem item : recordItemList) {
                item.setBidRecordId(newRecord.getBidRecordId());
            }
            //recordItemServic
            boolean b = recordItemService.saveBatch(recordItemList);
            if (!b) {
                throw new BusinessException("报价异常");
            }
            return;


        } else {
            //if (!biddingBidRecord.getState().equals(0) || biddingBidRecord.getState() != 0) {
            //    throw new BusinessException("已提交报价，不允许修改");
            //}
            BiddingPurchase purchase = purchaseService.lambdaQuery().eq(BiddingPurchase::getBiddingSn, biddingSn).one();
            Date date = new Date();
            if (date.after(purchase.getEndTime())) {
                throw new BusinessException("已经超过截止时间，无法提交报价");
            }
            // 明细存在则更新
            BigDecimal bidRecordAmount = new BigDecimal(0).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal bidRecordRateAmount = new BigDecimal(0).setScale(2, BigDecimal.ROUND_HALF_UP);
            List<BiddingBidRecordItem> list = recordItemService.lambdaQuery()
                    .eq(BiddingBidRecordItem::getBiddingId, biddingBidRecord.getBiddingId())
                    .eq(BiddingBidRecordItem::getBidRecordId, biddingBidRecord.getBidRecordId()).list();
            if (list != null && jsonArray != null) {
                if (list.size() != jsonArray.size()) {
                    throw new BusinessException("更新数量与系统数量不一致");
                }
            }
            for (BiddingBidRecordItem recordItem : list) {
                //循环更新竞价记录
                for (Object o : jsonArray) {
                    // 前端上传的数据
                    JSONObject object = JSONObject.parseObject(JSONObject.toJSONString(o));
                    Map<String, Object> map = object.getInnerMap();
                    String biddingProductId = (String) map.get("biddingProductId");
                    if (recordItem.getBiddingProductId().equals(biddingProductId)) {
                        // 根据数量计算价格数据
                        String num = String.valueOf(map.get("num"));
                        // 备注
                        String bidRatePriceStr = String.valueOf(map.get("bidRatePrice"));
                        String remarks = String.valueOf(map.get("remarks"));
                        if (StringUtils.isBlank(bidRatePriceStr) || StringUtils.isBlank(taxRateString)) {
                            throw new BusinessException("竞价商品数据不可为空");
                        }
                        BigDecimal bigNumDecimal = new BigDecimal(num);
                        // 含税单价
                        BigDecimal bidRatePrice = new BigDecimal(bidRatePriceStr).setScale(2, BigDecimal.ROUND_HALF_UP);
                        // 不含税单价
                        BigDecimal bidPrice = TaxCalculator.calculateNotTarRateAmount(bidRatePrice,taxRate);
                        // 限价
                        BiddingProduct p = biddingProductService.lambdaQuery().eq(BiddingProduct::getBiddingProductId, biddingProductId).one();
                        if (p.getReferencePrice() != null) {
                            // 含税到场大雨参考价异常
                            if (bidRatePrice.compareTo(p.getReferencePrice()) > 0 && p.getReferencePrice().compareTo(new BigDecimal(0)) > 0 ) {
                                throw new BusinessException(p.getProductName() + "含税单价大于最高参考单价");
                            }
                        }
                        // 结束
                        // 含税总金额 = 数量 X  含税到场单价
                        BigDecimal bidRateAmount = bidRatePrice.multiply(bigNumDecimal).setScale(2, BigDecimal.ROUND_HALF_UP);
                        bidRecordRateAmount = bidRecordRateAmount.add(bidRateAmount);
                        // 不含税总金额 = 数量 X 不含税到场单价
                        BigDecimal bidAmount = TaxCalculator.calculateNotTarRateAmount(bidRateAmount,taxRate);
                        boolean b = recordItemService.lambdaUpdate()
                                .eq(BiddingBidRecordItem::getBidRecordId, biddingBidRecord.getBidRecordId())
                                .eq(BiddingBidRecordItem::getBiddingProductId, biddingProductId)
                                .set(BiddingBidRecordItem::getBidPrice, bidPrice)
                                .set(BiddingBidRecordItem::getTaxRate, taxRate)
                                .set(BiddingBidRecordItem::getBidRatePrice, bidRatePrice)
                                .set(BiddingBidRecordItem::getBidAmount, bidAmount)
                                .set(BiddingBidRecordItem::getBidRateAmount, bidRateAmount)
                                .set(!remarks.equalsIgnoreCase("null"), BiddingBidRecordItem::getRemarks, remarks)
                                .update();
                        if (!b) {
                            throw new BusinessException("报价明细更新异常");
                        }
                    }
                }
            }
            bidRecordAmount = TaxCalculator.calculateNotTarRateAmount(bidRecordRateAmount,taxRate);
            boolean b = recordService.lambdaUpdate()
                    .eq(BiddingBidRecord::getBiddingSn, biddingSn)
                    .eq(BiddingBidRecord::getSupplierId, user.getEnterpriseId())
                    .eq(BiddingBidRecord::getBidRecordId, biddingBidRecord.getBidRecordId())
                    .set(BiddingBidRecord::getTaxRate,taxRate)
                    .set(BiddingBidRecord::getBidAmount, bidRecordAmount)
                    .set(BiddingBidRecord::getBidRateAmount, bidRecordRateAmount)
                    .update();
            if (!b) {
                throw new BusinessException("报价记录修改失败");
            }


        }


    }

    @Override
    @NotResubmit
    @Transactional(rollbackFor = Exception.class)
    public void saveFileAndStatus(String biddingRecordId, JSONObject jsonObject) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        if (org.springframework.util.StringUtils.isEmpty(biddingRecordId) || "null".equalsIgnoreCase(biddingRecordId)) {
            throw new BusinessException("biddingRecordId不可为空");
        }
        BiddingBidRecord record = recordService.lambdaQuery().select(BiddingBidRecord::getBiddingId).eq(BiddingBidRecord::getBidRecordId, biddingRecordId).one();
        BiddingPurchase purchase = purchaseService.lambdaQuery().select(BiddingPurchase::getEndTime).eq(BiddingPurchase::getBiddingId, record.getBiddingId()).one();
        Date date = new Date();
        if (date.after(purchase.getEndTime())) {
            throw new BusinessException("已经超过截止时间，无法提交报价");
        }
        boolean b = recordService.lambdaUpdate()
                .eq(BiddingBidRecord::getBidRecordId, biddingRecordId)
                //.eq(BiddingBidRecord::getSupplierId, user.getEnterpriseId())
                .set(BiddingBidRecord::getBidTime, new Date())
                .set(BiddingBidRecord::getSubmitDate, new Date())
                .set(BiddingBidRecord::getState, 1).update();
        if (!b) {
            throw new BusinessException("报价异常");
        }
        // 保存文件
        File file = new File();
        Map<String, Object> map = jsonObject.getInnerMap();
        if (map.size() == 0) {
            throw new BusinessException("请上传报价函");
        }
        File one = fileService.lambdaQuery().eq(File::getRelevanceId, biddingRecordId).eq(File::getRelevanceType, 15).one();
        String fileType = map.get("fileType").toString();
        String url = map.get("url").toString();
        String fileFarId = map.get("fileFarId").toString();
        String name = map.get("name").toString();
        if (one != null) {
            // 更新文件
            File newFile = new File();
            BeanUtils.copyProperties(one, newFile);
            newFile.setFileFarId(fileFarId);
            newFile.setUrl(url);
            newFile.setFileType(Integer.valueOf(fileType));
            newFile.setName(name);
            boolean update = fileService.updateById(newFile);
            if (!update) {
                throw new BusinessException("报价函上传异常");
            }
            return;
        }
        file.setRelevanceId(biddingRecordId);
        file.setFileType(Integer.valueOf(fileType));
        file.setRelevanceType(15);
        file.setUrl(url);
        file.setFileFarId(fileFarId);
        //file.setIsMain(0);
        //file.setFileId(fileFarId);
        file.setName(name);
        fileService.create(file);

    }

    @Override
    public BidRowInfo getProductInfo(JSONObject jsonObject) {
        BidRowInfo bidRowInfo = new BidRowInfo();
        Map<String, Object> map = jsonObject.getInnerMap();
        if (map.get("biddingProductId") == null || map.get("biddingSn") == null) {
            throw new BusinessException("竞价编号或商品ID不可为空");
        }
        String biddingProductId = map.get("biddingProductId").toString();
        String biddingSn = map.get("biddingSn").toString();
        BiddingProduct one = biddingProductService.lambdaQuery()
                .eq(BiddingProduct::getBiddingSn, biddingSn)
                .eq(BiddingProduct::getBiddingProductId, biddingProductId)
                .one();
        bidRowInfo.setProductName(one.getProductName());
        bidRowInfo.setBrand(one.getBrand());
        bidRowInfo.setSpec(one.getSpec());
        bidRowInfo.setUnit(one.getUnit());
        bidRowInfo.setNum(one.getNum());
        bidRowInfo.setProductTexture(one.getProductTexture());
        bidRowInfo.setReferencePrice(one.getReferencePrice());
        bidRowInfo.setDeliveryDate(one.getDeliveryDate());
        bidRowInfo.setDeliveryAddress(one.getDeliveryAddress());
        bidRowInfo.setRemarks(one.getRemarks());
        return bidRowInfo;
    }

    @Override
    public void exportBidLetterExcel(String biddingSn, HttpServletResponse response) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String suId = user.getEnterpriseId();
        BiddingBidRecord record = recordService.
                lambdaQuery().eq(BiddingBidRecord::getBiddingSn, biddingSn)
                .eq(BiddingBidRecord::getSupplierId, suId).one();
        if (ObjectUtils.isEmpty(record)) {
            throw new BusinessException("请先点击确认修改报价，再下载报价文件！");
        }
        // 发起方
        BiddingPurchase purchase = purchaseService.lambdaQuery().eq(BiddingPurchase::getBiddingId, record.getBiddingId()).one();
        String createOrgName = purchase.getCreateOrgName();
        Date endTime = purchase.getEndTime();
        // 竞价方
        String enterpriseName = user.getEnterpriseName();
        // 获取数据库商品明细
        BidOfferVo bidInfo = getBidOfferVoByBidSn(biddingSn);
        // 商品数量统计
        BigDecimal numAmount = new BigDecimal(0).setScale(3, BigDecimal.ROUND_HALF_UP);
        for (BidOfferInfoVo infoVo : bidInfo.getProductList()) {
            infoVo.setNum(infoVo.getNum().setScale(3, BigDecimal.ROUND_HALF_UP));
            numAmount = numAmount.add(infoVo.getNum().setScale(3, BigDecimal.ROUND_HALF_UP));
        }
        for (BidOfferInfoVo vo : bidInfo.getProductList()) {
            if (!ObjectUtils.isEmpty(vo.getTaxRate())) {
                vo.getTaxRate().divide(new BigDecimal(100));
            }

        }
        try {

            String src = mallConfig.templateFormUrl;
            //String src = "/Volumes/westDisk/work/template/竞价模板";
            Map<String, Object> dataMap = new HashMap<>();
            // 编号
            dataMap.put("bidSn", bidInfo.getBiddingSn());
            dataMap.put("dataList", bidInfo.getProductList());
            dataMap.put("createOrgName", createOrgName);
            dataMap.put("enterpriseName", enterpriseName.toString());
            dataMap.put("numAmount", numAmount);
            String endTimeStr = DateUtil.getyyymmddHHmmss(endTime).toString();
            String[] times = endTimeStr.split(" ");
            dataMap.put("year", times[0]);
            dataMap.put("month", times[1]);
            dataMap.put("day", times[2]);
            dataMap.put("minutes", times[3]);
            dataMap.put("taxRate", record.getTaxRate());
            dataMap.put("bidRateAmount", record.getBidRateAmount().toString());
            dataMap.put("bidAmount", record.getBidAmount().toString());
            dataMap.put("biddingNotice", purchase.getBiddingNotice());
            // 竞价函说明

            // 大宗商品
            if (purchase.getProductType() == 1){
                if (purchase.getBillType() == 1){
                    // 浮动
                    ExcelForWebUtil.exportExcel(response, dataMap, "大宗浮动竞价函模板.xlsx", src, "竞价函.xlsx");
                    return;
                }if (purchase.getBillType() == 2){
                    // 固定
                    ExcelForWebUtil.exportExcel(response, dataMap, "大宗固定竞价函模板.xlsx", src, "竞价函.xlsx");
                    return;
                }
            }
            // 大宗临购清单
            if (purchase.getBiddingSourceType() == 3){
                if (purchase.getBillType() == 1){
                    // 浮动
                    ExcelForWebUtil.exportExcel(response, dataMap, "大宗临购清单浮动竞价函模板.xlsx", src, "竞价函.xlsx");
                    return;
                }if (purchase.getBillType() == 2){
                    // 固定
                    ExcelForWebUtil.exportExcel(response, dataMap, "大宗临购清单固定竞价函模板.xlsx", src, "竞价函.xlsx");
                    return;
                }
            }
            ExcelForWebUtil.exportExcel(response, dataMap, "竞价函模板.xlsx", src, "竞价函.xlsx");
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BusinessException(500, "导出失败");
        }


    }

    @Override
    public void saveFileRecord() {

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void udateMyLgOfferPrice(JSONObject jsonObject, String biddingSn) {
        if (StringUtils.isBlank(biddingSn)) {
            throw new BusinessException("竞价编号为空");
        }
        // 竞价结束或者超时不允许报价
        BiddingPurchase purchase = purchaseService.lambdaQuery().eq(BiddingPurchase::getBiddingSn, biddingSn).one();
        if (ObjectUtils.isEmpty(purchase)) {
            throw new BusinessException("竞价数据不存在");
        }
        Date date = new Date();
        if (date.after(purchase.getEndTime())) {
            throw new BusinessException("已经超过截止时间，无法提交报价");
        }
        // 不是审核通过状态不允许报价
        if (purchase.getState() != 5) {
            throw new BusinessException("当前竞价已结束或未开始");
        }

        // 已经确认提交报价不能再修改
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        BiddingBidRecord biddingBidRecord = recordService.lambdaQuery()
                .eq(BiddingBidRecord::getBiddingSn, biddingSn)
                .eq(BiddingBidRecord::getSupplierId, user.getEnterpriseId())
                .one();
        // 1浮动价格2固定价格
        MyBiddingService biddingService = SpringBeanUtil.getBean(MyBiddingService.class);
        if (biddingBidRecord != null) {
            //if (biddingBidRecord.getState() != 0) {
            //    throw new BusinessException("已经提交报价，不允许修改");
            //}
            // 修改报价BiddingBidRecord记录
            biddingService.handleExistingBidRecord(purchase,jsonObject, biddingBidRecord);

        } else {
            biddingService.handleNewBidRecord(purchase, jsonObject, biddingSn);
        }


    }
    @Override
    public void handleNewBidRecord(BiddingPurchase purchase,JSONObject jsonObject, String biddingSn) {
        // 处理新的竞价记录的逻辑
        // 新增 BiddingBidRecord 表
        Integer billType = purchase.getBillType();
        // 拿税率
        String taxRateS = String.valueOf(jsonObject.get("taxRate") == null ? "0" : jsonObject.get("taxRate"));
        ArrayList tableData = (ArrayList) jsonObject.get("tableData");
        BiddingBidRecord record = new BiddingBidRecord();
        record.setBidRecordSn(CodeGenerator.generateUniqueCode());
        record.setBiddingSn(biddingSn);
        record.setBiddingId(purchase.getBiddingId());
        record.setBillType(billType);
        record.setState(0);
        record.setTaxRate(new BigDecimal(taxRateS).setScale(2, BigDecimal.ROUND_HALF_UP));
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        record.setSupplierId(currentUser.getEnterpriseId());
        record.setSupplierName(currentUser.getEnterpriseName());
        int insert = recordService.getBaseMapper().insert(record);
        if (insert == 0) {
            throw new BusinessException("报价记录异常");
        }
        // 不含税总金额
        BigDecimal bidRecordAmount = new BigDecimal(0).setScale(2, BigDecimal.ROUND_HALF_UP);
        // 含税总金额
        BigDecimal bidRecordRateAmount = new BigDecimal(0).setScale(2, BigDecimal.ROUND_HALF_UP);
        // 报价记录列表
        List<BiddingBidRecordItem> recordItemList = new ArrayList<>();
        // 处理明细数据
        for (Object o : tableData) {
            // 明细对象
            BiddingBidRecordItem recordItem = new BiddingBidRecordItem();
            JSONObject productPriceTem = JSONObject.parseObject(JSON.toJSONString(o));
            Map<String, Object> innerMap = productPriceTem.getInnerMap();
            String biddingProductId = (String) innerMap.get("biddingProductId");
            recordItem.setBiddingProductId(biddingProductId);
            BiddingProduct product = biddingProductService.lambdaQuery().eq(BiddingProduct::getBiddingProductId, biddingProductId).one();
            if (product == null) {
                throw new BusinessException("竞价商品不存在");
            }
            // 税率
            //String taxRateS = (String) innerMap.get("taxRate");
            //String taxRateS = String.valueOf(innerMap.get("taxRate"));
            BigDecimal taxRate = new BigDecimal(taxRateS).setScale(2, BigDecimal.ROUND_HALF_UP);
            recordItem.setTaxRate(taxRate);
            // 根据类型获取价格 1浮动价格 网价 固定费 2固定价格 出厂价 运杂费
            BigDecimal price1 = null; // 网价或者出厂价
            BigDecimal price2 = null; // 固定费或者运杂费
            if (billType == 1) {
                String netPrice = String.valueOf(innerMap.get("netPrice"));

                if (StringUtils.isBlank(netPrice)) {
                    throw new BusinessException("网价不可为空");
                }
                price1 = new BigDecimal(netPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
                if (price1.compareTo(new BigDecimal(0)) < 0) {
                    throw new BusinessException("网价不可小于0");
                }
                recordItem.setNetPrice(price1);
                String fixationPrice = String.valueOf(innerMap.get("fixationPrice"));
                if (StringUtils.isBlank(fixationPrice)) {
                    fixationPrice = "0";
                }
                price2 = new BigDecimal(fixationPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
                recordItem.setFixationPrice(price2);
            } else {
                String outFactoryPrice = String.valueOf(innerMap.get("outFactoryPrice"));
                if (StringUtils.isBlank(outFactoryPrice)) {
                    throw new BusinessException("出厂价不可为空");
                }
                price1 = new BigDecimal(outFactoryPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
                if (price1.compareTo(new BigDecimal(0)) < 0) {
                    throw new BusinessException("出厂价不可小于0");
                }
                recordItem.setOutFactoryPrice(price1);
                String transportPrice = String.valueOf(innerMap.get("transportPrice"));
                if (StringUtils.isBlank(transportPrice)) {
                    transportPrice = "0";
                }
                price2 = new BigDecimal(transportPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
                recordItem.setTransportPrice(price2);
            }
            String remarks = (String) innerMap.get("remarks");
            recordItem.setRemarks(remarks);
            // 计算不含税单价 含税总金额
            BigDecimal p1 = price1;
            BigDecimal p2 = price2;
            // 含税单价
            BigDecimal bidRatePrice = p1.add(p2).setScale(2, BigDecimal.ROUND_HALF_UP);
            // 限价
            if (product.getReferencePrice() != null) {
                // 出厂价大于限价
                if (bidRatePrice.compareTo(product.getReferencePrice()) > 0 && product.getReferencePrice().compareTo(new BigDecimal(0)) > 0) {
                    throw new BusinessException(product.getProductName() + "含税单价大于最高单价");
                }
            }
            recordItem.setBidRatePrice(bidRatePrice);
            // 含税总金额
            BigDecimal bidRateAmount = bidRatePrice.multiply(product.getNum()).setScale(2, BigDecimal.ROUND_HALF_UP);
            recordItem.setBidRateAmount(bidRateAmount);
            bidRecordRateAmount = bidRecordRateAmount.add(bidRateAmount);
            // 计算不含税单价 不含税总金额
            BigDecimal bidPrice = TaxCalculator.calculateNotTarRateAmount(bidRatePrice, taxRate);
            recordItem.setBidPrice(bidPrice);
            BigDecimal bidAmount = TaxCalculator.calculateNotTarRateAmount(bidRateAmount, taxRate);
            recordItem.setBidAmount(bidAmount);
            bidRecordAmount = bidRecordAmount.add(bidAmount);
            recordItem.setBiddingId(purchase.getBiddingId());
            // 关联主表
            recordItem.setBidRecordId(record.getBidRecordId());
            recordItemList.add(recordItem);
        }
        // 修改报价主表总金额
        //bidRecordAmount =  TaxCalculator.calculateNotTarRateAmount(bidRecordRateAmount, record.getTaxRate());
        record.setBidAmount(bidRecordAmount);
        record.setBidRateAmount(bidRecordRateAmount);
        int update = recordService.getBaseMapper().updateById(record);
        boolean b = recordItemService.saveBatch(recordItemList);
        if (update == 0 || !b) {
            throw new BusinessException("报价记录异常");
        }
    }

    @Override
    public void handleExistingBidRecord(BiddingPurchase purchase,JSONObject jsonObject, BiddingBidRecord biddingBidRecord) {
        // 处理已存在的竞价记录的逻辑
        // 修改 BiddingBidRecord
        Integer billType = purchase.getBillType();
        // 拿税率
        String taxRateS = String.valueOf(jsonObject.get("taxRate"));
        ArrayList tableData = (ArrayList) jsonObject.get("tableData");
        // 根据record查询明细
        List<BiddingBidRecordItem> recordItemList = recordItemService.lambdaQuery()
                .eq(BiddingBidRecordItem::getBidRecordId, biddingBidRecord.getBidRecordId())
                .list();
        // 不含税总金额
        BigDecimal bidRecordAmount = new BigDecimal(0).setScale(2, BigDecimal.ROUND_HALF_UP);
        // 含税总金额
        BigDecimal bidRecordRateAmount = new BigDecimal(0).setScale(2, BigDecimal.ROUND_HALF_UP);
        //  批量修改明细
        List<BiddingBidRecordItem> updateList = new ArrayList<>();
        for (BiddingBidRecordItem recordItem : recordItemList) {
            for (Object o : tableData) {
                JSONObject productPriceTem = JSONObject.parseObject(JSON.toJSONString(o));
                Map<String, Object> innerMap = productPriceTem.getInnerMap();
                String biddingProductId = (String) innerMap.get("biddingProductId");
                if (recordItem.getBiddingProductId().equals(biddingProductId)) {
                    // 税率
                    BiddingProduct product = biddingProductService.lambdaQuery().eq(BiddingProduct::getBiddingProductId, biddingProductId).one();
                    //String taxRateS = (String) innerMap.get("taxRate");
                    //String taxRateS =  String.valueOf(innerMap.get("taxRate"));

                    BigDecimal taxRate = new BigDecimal(taxRateS).setScale(2, BigDecimal.ROUND_HALF_UP);
                    recordItem.setTaxRate(taxRate);
                    // 根据类型获取价格 1浮动价格 网价 固定费 2固定价格 出厂价 运杂费
                    BigDecimal price1 = null; // 网价或者出厂价
                    BigDecimal price2 = null; // 固定费或者运杂费
                    if (billType == 1) {
                        String netPrice =  String.valueOf(innerMap.get("netPrice"));

                        if (StringUtils.isBlank(netPrice)) {
                            throw new BusinessException("网价不可为空");
                        }
                        price1 = new BigDecimal(netPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
                        if (price1.compareTo(new BigDecimal(0)) < 0) {
                            throw new BusinessException("网价不可小于0");
                        }
                        recordItem.setNetPrice(price1);
                        String fixationPrice =  String.valueOf(innerMap.get("fixationPrice"));
                        price2 = new BigDecimal(fixationPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
                        recordItem.setFixationPrice(price2);
                    }
                    if (billType == 2) {
                        String outFactoryPrice =  String.valueOf(innerMap.get("outFactoryPrice"));
                        if (StringUtils.isBlank(outFactoryPrice)) {
                            throw new BusinessException("出厂价不可为空");
                        }
                        price1 = new BigDecimal(outFactoryPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
                        if (price1.compareTo(new BigDecimal(0)) < 0) {
                            throw new BusinessException("出厂价不可小于0");
                        }
                        recordItem.setOutFactoryPrice(price1);
                        String transportPrice =  String.valueOf(innerMap.get("transportPrice"));
                        price2 = new BigDecimal(transportPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
                        recordItem.setTransportPrice(price2);
                    }
                    String remarks = (String) innerMap.get("remarks");
                    recordItem.setRemarks(remarks);
                    // 计算含税单价 含税总金额
                    BigDecimal p1 = price1;
                    BigDecimal p2 = price2;
                    // 含税单价
                    BigDecimal bidRatePrice = p1.add(p2).setScale(2, BigDecimal.ROUND_HALF_UP);
                    // 限价
                    if (product.getReferencePrice() != null) {
                        // 出厂价大于限价
                        if (bidRatePrice.compareTo(product.getReferencePrice()) > 0 && product.getReferencePrice().compareTo(new BigDecimal(0)) > 0) {
                            throw new BusinessException(product.getProductName() + "含税单价大于最高单价");
                        }
                    }
                    recordItem.setBidRatePrice(bidRatePrice);
                    // 含税总金额
                    BigDecimal bidRateAmount = bidRatePrice.multiply(product.getNum()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    recordItem.setBidRateAmount(bidRateAmount);
                    bidRecordRateAmount = bidRecordRateAmount.add(bidRateAmount);
                    // 计算不含税单价 不含税总金额
                    BigDecimal bidPrice = TaxCalculator.calculateNotTarRateAmount(bidRatePrice, taxRate);
                    recordItem.setBidPrice(bidPrice);
                    //BigDecimal bidAmount = bidPrice.multiply(product.getNum()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    BigDecimal bidAmount = TaxCalculator.calculateNotTarRateAmount(bidRateAmount, taxRate);
                    recordItem.setBidAmount(bidAmount);
                    bidRecordAmount = bidRecordAmount.add(bidAmount);
                    updateList.add(recordItem);

                }
            }
        }
        // 税率
        biddingBidRecord.setTaxRate(new BigDecimal(taxRateS).setScale(2, BigDecimal.ROUND_HALF_UP));
        // 修改报价主表总金额
        //bidRecordAmount =  TaxCalculator.calculateNotTarRateAmount(bidRecordRateAmount, biddingBidRecord.getTaxRate());
        biddingBidRecord.setBidAmount(bidRecordAmount);
        biddingBidRecord.setBidRateAmount(bidRecordRateAmount);
        int update = recordService.getBaseMapper().updateById(biddingBidRecord);
        boolean b = recordItemService.updateBatchById(updateList);
        if (update == 0 || !b) {
            throw new BusinessException("报价记录异常");
        }
    }
}
