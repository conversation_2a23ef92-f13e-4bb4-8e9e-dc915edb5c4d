package scrbg.meplat.mall.service.stockManage.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang3.StringUtils;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.OutboundSettlementManage;
import scrbg.meplat.mall.mapper.stockManage.OutboundSettlementManageMapper;
import scrbg.meplat.mall.service.stockManage.OutboundSettlementService;
import scrbg.meplat.mall.util.excel.EasyExcelUtils;
import scrbg.meplat.mall.vo.stockManage.OutboundSettlementManageVO;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OutboundSettlementServiceImpl  extends ServiceImpl<OutboundSettlementManageMapper, OutboundSettlementManage> implements OutboundSettlementService {


    private OutboundSettlementManageMapper outboundSettlementManageMapper;


    @Autowired
    public void setOutboundSettlementManageMapper(OutboundSettlementManageMapper outboundSettlementManageMapper) {
        this.outboundSettlementManageMapper = outboundSettlementManageMapper;
    }
    @Override
    public void saveSettlement(OutboundSettlementManage outboundSettlement) {
        outboundSettlement.setAuditStatus(0);
        outboundSettlement.setReceiveName(ThreadLocalUtil.getCurrentUser().getUserName());
        outboundSettlement.setReceivePhone(ThreadLocalUtil.getCurrentUser().getUserMobile());
        outboundSettlement.setGmtCreate(new Date());
        save(outboundSettlement);
    }

    @Override
    public void saveAndSubmitSettlement(OutboundSettlementManage outboundSettlement) {
        outboundSettlement.setReceiveName(ThreadLocalUtil.getCurrentUser().getUserName());
        outboundSettlement.setReceivePhone(ThreadLocalUtil.getCurrentUser().getUserMobile());
        outboundSettlement.setGmtCreate(new Date());
        outboundSettlement.setPeriodsNum("");
        outboundSettlement.setWarehouseId("1");
        outboundSettlement.setAuditStatus(1);
        save(outboundSettlement);
    }

    @Override
    public PageUtils<OutboundSettlementManage> queryPage(JSONObject jsonObject, LambdaQueryWrapper<OutboundSettlementManage> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String contractNo = (String) innerMap.get("contractNo");
        String purchasingOrgName = (String) innerMap.get("purchasingOrgName");
        Integer outboundType = (Integer) innerMap.get("outboundType");
        Integer auditStatus = (Integer) innerMap.get("auditStatus");
        String startCreateDate = (String) innerMap.get("startCreateDate");
        String endCreateDate = (String) innerMap.get("endCreateDate");
        if (StringUtils.isNotBlank(keywords)) {
            queryWrapper.and((t) -> {
                t.like(OutboundSettlementManage::getContractNo, keywords)
                        .or()
                        .like(OutboundSettlementManage::getPurchasingOrgName, keywords);

            });
        }
        if (!StringUtils.isEmpty(contractNo)) {
            queryWrapper.like(OutboundSettlementManage::getContractNo, contractNo);
        }
        if ( null != outboundType) {
            queryWrapper.eq(OutboundSettlementManage::getOutboundType, outboundType);
        }
        if ( null != auditStatus) {
            queryWrapper.eq(OutboundSettlementManage::getAuditStatus, auditStatus);
        }
        if (!StringUtils.isEmpty(purchasingOrgName)) {
            queryWrapper.like(OutboundSettlementManage::getPurchasingOrgName, purchasingOrgName);
        }
        queryWrapper.between(StringUtils.isNotBlank(startCreateDate) && StringUtils.isNotEmpty(endCreateDate), OutboundSettlementManage::getGmtCreate, startCreateDate, endCreateDate);
        IPage<OutboundSettlementManage> page = this.page(
                new Query<OutboundSettlementManage>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils<OutboundSettlementManage>(page);
    }

    @Override
    public void export(JSONObject jsonObject, HttpServletResponse response) {
        LambdaQueryWrapper<OutboundSettlementManage> queryWrapper = new LambdaQueryWrapper<>();
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String contractNo = (String) innerMap.get("contractNo");
        String purchasingOrgName = (String) innerMap.get("purchasingOrgName");
        Integer outboundType = (Integer) innerMap.get("outboundType");
        Integer auditStatus = (Integer) innerMap.get("auditStatus");
        String startCreateDate = (String) innerMap.get("startCreateDate");
        String endCreateDate = (String) innerMap.get("endCreateDate");
        if (StringUtils.isNotBlank(keywords)) {
            queryWrapper.and((t) -> {
                t.like(OutboundSettlementManage::getContractNo, keywords)
                        .or()
                        .like(OutboundSettlementManage::getPurchasingOrgName, keywords);

            });
        }
        if (!StringUtils.isEmpty(contractNo)) {
            queryWrapper.like(OutboundSettlementManage::getContractNo, contractNo);
        }
        if ( null != outboundType) {
            queryWrapper.eq(OutboundSettlementManage::getOutboundType, outboundType);
        }
        if ( null != auditStatus) {
            queryWrapper.eq(OutboundSettlementManage::getAuditStatus, auditStatus);
        }
        if (!StringUtils.isEmpty(purchasingOrgName)) {
            queryWrapper.like(OutboundSettlementManage::getPurchasingOrgName, purchasingOrgName);
        }
        queryWrapper.between(StringUtils.isNotBlank(startCreateDate) && StringUtils.isNotEmpty(endCreateDate), OutboundSettlementManage::getGmtCreate, startCreateDate, endCreateDate);
        List<OutboundSettlementManage> list = outboundSettlementManageMapper.selectList(queryWrapper);
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", list);
        try {
            List<OutboundSettlementManageVO> listVo =list.stream().map(item->{
                OutboundSettlementManageVO vo = new OutboundSettlementManageVO();
                vo.setSupplierType( 0 == item.getSupplierType() ? "零星采购" : 1 == item.getSupplierType() ? "大宗临购":"周转材料");
                vo.setSupplierName(item.getSupplierName());
                vo.setContractNo(item.getContractNo());
                vo.setPurchasingOrgName(item.getPurchasingOrgName());
                vo.setOutboundType(item.getOutboundType() == 1 ? "手动入库" : "自动入库");
                vo.setCgRateAmount(item.getCgRateAmount());
                vo.setCgNoRateAmount(item.getCgNoRateAmount());
                vo.setXsRateAmount(item.getXsRateAmount());
                vo.setXsNoRateAmount(item.getXsNoRateAmount());
                vo.setNum(item.getNum());
                vo.setReceiveName(item.getReceiveName());
                vo.setReceivePhone(item.getReceivePhone());
                return vo;
            }).collect(Collectors.toList());
            EasyExcelUtils.writeWeb("出库结算单",OutboundSettlementManageVO.class, listVo, "出库结算单", response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void updateState(String id, int state) {
        OutboundSettlementManage manage = new OutboundSettlementManage();
        manage.setId(id);
        manage.setAuditStatus(state);
        updateById(manage);
    }
}
