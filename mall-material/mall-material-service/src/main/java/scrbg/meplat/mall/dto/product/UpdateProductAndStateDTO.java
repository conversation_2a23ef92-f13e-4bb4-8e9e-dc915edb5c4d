package scrbg.meplat.mall.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.File;
import scrbg.meplat.mall.entity.RegionPrice;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @program: maill_api
 * @description: 物资保存并提交上架对象
 * @author: 代文翰
 * @create: 2023-10-12 13:41
 **/
@Data

public class UpdateProductAndStateDTO {
    // 状态
    @ApiModelProperty(value = "商品id",required = true)
    @NotEmpty(message = "商品id不能为空！")
    private List<String> productIds;

    @ApiModelProperty(value = "商品状态（0待上架 1已上架 2已下架 3 待审核 4审核失败）",required = true)
    @NotNull(message = "商品状态不能为空！")
    private Integer state;

    @ApiModelProperty(value = "审核失败原因")
    private String failReason;
    // 物资商品
    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "商品类型 0物资 2周材（物资）")
    private Integer productType;



    @ApiModelProperty(value = "关联名称（用于关联外部的名称唯一不修改）")
    private String relevanceName;

    @ApiModelProperty(value = "关联外部id")
    private String relevanceId;

    @ApiModelProperty(value = "关联编号")
    private String relevanceNo;



    @ApiModelProperty(value = "分类路径")
    private List<String> classPath;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品关键字")
    private String productKeyword;

    @ApiModelProperty(value = "分类id")
    private String classId;

    @ApiModelProperty(value = "商品的最低价")
    private BigDecimal productMinPrice;

    @ApiModelProperty(value = "品牌id")
    private String brandId;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "县、区")
    private String county;

    @ApiModelProperty(value = "详细地址")
    private String detailedAddress;

    @ApiModelProperty(value = "店铺排序")
    private Integer shopSort;

    @ApiModelProperty(value = "商品描述")
    private String productDescribe;

    //@ApiModelProperty(value = "商品状态（0待上架 1已上架 2已下架 3 待审核 4审核失败）")
    //
    //private Integer state;

    /**
     * file
     */

    @ApiModelProperty(value = "商品主图")
    private List<File> adminFile;

    @ApiModelProperty(value = "商品小图")
    private List<File> minFile;

    @ApiModelProperty(value = "商品图片")
    private List<File> productFiles;

    /**
     * sku
     */

    @ApiModelProperty(value = "skuid")
    private String skuId;

    @ApiModelProperty(value = "sku名称")
    private String skuName;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "销售价格")
    private BigDecimal sellPrice;

    @ApiModelProperty(value = "库存")
    private BigDecimal stock;

    @ApiModelProperty(value = "规格单位")
    private String unit;

    @ApiModelProperty(value = "结算价")
    private BigDecimal settlePrice;
    @ApiModelProperty(value = "商品材质")

    private String productTexture;
    @ApiModelProperty(value = "副计量单位")
    private String secondUnit;
    @ApiModelProperty(value = "临购副单位系数")
    private BigDecimal secondUnitNum;

    @ApiModelProperty(value = "区域类型（1全区域,2区域）")
    private Integer isZone;



    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "商品对应区域价格")
    private List<RegionPrice> regionPrice;


    @ApiModelProperty(value = "年化率")
    private BigDecimal annualizedRate;

    @ApiModelProperty(value = "自营店商品-采购进价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "自营店商品-账期")
    private String accountPeriod;

    @ApiModelProperty(value = "加成率 1固定 2自定义")
    private Integer markUp;

    @ApiModelProperty(value = "加成率")
    private BigDecimal markUpNum;

    @ApiModelProperty(value = "价格类型：0 一口价 1 参考价")
    private Integer priceType;

}
