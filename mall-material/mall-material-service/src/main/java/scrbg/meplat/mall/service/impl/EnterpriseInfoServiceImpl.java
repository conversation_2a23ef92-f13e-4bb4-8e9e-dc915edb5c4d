package scrbg.meplat.mall.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;

import scrbg.meplat.mall.common.redis.RedisKey;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.entity.excelTemplate.Supplier;
import scrbg.meplat.mall.enums.CodeEnum;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.enums.enterprise.EnterpriseEnum;
import scrbg.meplat.mall.enums.user.UserEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.EnterpriseInfoMapper;
import scrbg.meplat.mall.mapper.ShopMapper;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.PcwpService;
import scrbg.meplat.mall.pcwp.auth.model.SignUp;
import scrbg.meplat.mall.pcwp.auth.model.SupplierRes;
import scrbg.meplat.mall.pcwp.auth.model.TokenRes;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.*;
import scrbg.meplat.mall.util.excel.EasyExcelUtils;
import scrbg.meplat.mall.vo.platform.enterprise.EnterpriseLedgerVo;
import scrbg.meplat.mall.vo.platform.RegisterPcwpFileVo;
import scrbg.meplat.mall.vo.platform.enterprise.EnterpriseArrearageVo;
import scrbg.meplat.mall.vo.user.ImportSupplerExcelResultVO;
import scrbg.meplat.mall.vo.user.userCenter.ShopStateVO;

/**
 * <AUTHOR>
 * @description 针对表【enterprise_info】的数据库操作Service实现
 * @createDate 2022-11-03 11:46:18
 */
@Service
public class EnterpriseInfoServiceImpl extends ServiceImpl<EnterpriseInfoMapper, EnterpriseInfo>
        implements EnterpriseInfoService {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private ShudaoEnterpriseService shudaoEnterpriseService;

    @Autowired
    private ShopSupplierReleService shopSupplierReleService;

    @Autowired
    ShopService shopService;

    @Autowired
    ImportSupplerService importSupplerService;

    @Autowired
    FileService fileService;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    UserService userService;
    @Autowired
    EnterpriseInfoService enterpriseInfoService;
    @Autowired
    private RestTemplateUtils restTemplateUtils;

    @Autowired
    MallConfig mallConfig;

    @Autowired
    ShopMapper shopMapper;
    @Autowired
    private PcwpService pcwpService;
    @Value("${app.pcwp.reset-password}")
    private String resetPassword;

    @Autowired
    EnterprisePerformanceService enterprisePerformanceService;
    @Autowired
    ContractService contractService;
    @Autowired
    PlatformYearFeeService platformYearFeeService;
    @Autowired
    PlatformDealFeeDtlService platformDealFeeDtlService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<EnterpriseInfo> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String enterpriseName = (String) innerMap.get("enterpriseName");
        Integer enterpriseType = (Integer) innerMap.get("enterpriseType");
        Integer isSupplier = (Integer) innerMap.get("isSupplier");
        String city = (String) innerMap.get("city");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        Integer isShortCode = (Integer) innerMap.get("isShortCode");
        Integer shuDaoFlag = (Integer) innerMap.get("shuDaoFlag");
        String startDate = (String) innerMap.get("startDate");
        String endDate = (String) innerMap.get("endDate");
        String keywords = (String) innerMap.get("keywords");
        Integer mallType = (Integer) innerMap.get("mallType");
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        if (isShortCode != null) {
            if (isShortCode == 0) {
                queryWrapper.isNull(EnterpriseInfo::getShortCode);
            } else {
                queryWrapper.isNotNull(EnterpriseInfo::getShortCode);
            }
        }
        if (shuDaoFlag != null) {
            queryWrapper.eq(EnterpriseInfo::getShuDaoFlag, shuDaoFlag);
        }
        if (mallType == 1) {
            queryWrapper.eq(EnterpriseInfo::getIsDeviceMall, 1);
        }
        if (mallType == 0) {
            queryWrapper.eq(EnterpriseInfo::getIsMaterialMall, 1);
        }
        if (!StringUtils.isEmpty(enterpriseName)) {
            queryWrapper.like(EnterpriseInfo::getEnterpriseName, enterpriseName);
        }
        if (enterpriseType != null) {
            queryWrapper.eq(EnterpriseInfo::getEnterpriseType, enterpriseType);
        }
        if (isSupplier != null) {
            queryWrapper.eq(EnterpriseInfo::getIsSupplier, isSupplier);
        }
        if (!StringUtils.isEmpty(city)) {
            queryWrapper.like(EnterpriseInfo::getCity, city);
        }
        if (!StringUtils.isEmpty(keywords)) {
            queryWrapper.and((t) -> t.like(EnterpriseInfo::getEnterpriseName, keywords)
                    .or()
                    .like(EnterpriseInfo::getCity, keywords));
        }
        queryWrapper.between(org.apache.commons.lang.StringUtils.isNotEmpty(startDate) && org.apache.commons.lang.StringUtils.isNotEmpty(endDate), EnterpriseInfo::getGmtCreate, startDate, endDate);
        //排序方式(排序值升序)
        if (orderBy == null) {
        } else if (orderBy == PublicEnum.ORDER_BY_SORT.getCode()) {
            queryWrapper.orderByDesc(EnterpriseInfo::getSort).orderByDesc(EnterpriseInfo::getGmtCreate);
            //排序方式(修改时间降序)
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_MODIFIED.getCode()) {
            queryWrapper.orderByDesc(EnterpriseInfo::getGmtModified);
            //排序方式(创建时间降序)
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_CREATE.getCode()) {
            queryWrapper.orderByDesc(EnterpriseInfo::getGmtCreate);
        }
        queryWrapper.ne(EnterpriseInfo::getEnterpriseType, 2);
        IPage<EnterpriseInfo> page = this.page(
                new Query<EnterpriseInfo>().getPage(jsonObject),
                queryWrapper
        );
        for(EnterpriseInfo enterpriseInfo : page.getRecords()) {
            List<EnterprisePerformance> list = enterprisePerformanceService.list(new QueryWrapper<EnterprisePerformance>().lambda()
                    .eq(EnterprisePerformance::getEnterpriseId, enterpriseInfo.getEnterpriseId()).orderByDesc(EnterprisePerformance::getGmtCreate));
            enterpriseInfo.setEpLists(list);
        }
        return new PageUtils(page);
    }

    @Override
    public PageUtils queryPageAll(JSONObject jsonObject, LambdaQueryWrapper<EnterpriseInfo> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String enterpriseName = (String) innerMap.get("enterpriseName");
        Integer enterpriseType = (Integer) innerMap.get("enterpriseType");
        Integer state = (Integer) innerMap.get("state");
        String startDate = (String) innerMap.get("startDate");
        String endDate = (String) innerMap.get("endDate");
        Boolean interiorId = (Boolean) innerMap.get("interiorId");
        Integer isSupplier = (Integer) innerMap.get("isSupplier");
        Integer isPcwp = (Integer) innerMap.get("isPcwp");
        Integer mallType = (Integer) innerMap.get("mallType");
        String city = (String) innerMap.get("city");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        if (mallType == 1) {
            queryWrapper.eq(EnterpriseInfo::getIsDeviceMall, 1);
        }
        if (mallType == 0) {
            queryWrapper.eq(EnterpriseInfo::getIsMaterialMall, 1);
        }
        if (!StringUtils.isEmpty(enterpriseName)) {
            queryWrapper.like(EnterpriseInfo::getEnterpriseName, enterpriseName);
        }
        if (!StringUtils.isEmpty(interiorId)) {
            if (interiorId) {
                queryWrapper.isNotNull(EnterpriseInfo::getInteriorId);
            } else {
                queryWrapper.isNull(EnterpriseInfo::getInteriorId);
            }
        }
        if (enterpriseType != null) {
            queryWrapper.eq(EnterpriseInfo::getEnterpriseType, enterpriseType);
        }
        queryWrapper.between(org.apache.commons.lang.StringUtils.isNotEmpty(startDate) && org.apache.commons.lang.StringUtils.isNotEmpty(endDate), EnterpriseInfo::getGmtCreate, startDate, endDate);
        if (isSupplier != null) {
            queryWrapper.eq(EnterpriseInfo::getIsSupplier, isSupplier);
        }
        if (isPcwp != null) {
            queryWrapper.eq(EnterpriseInfo::getIsPcwp, isPcwp);
        }
        if (state != null) {
            queryWrapper.eq(EnterpriseInfo::getState, state);
        }
        if (!StringUtils.isEmpty(city)) {
            queryWrapper.like(EnterpriseInfo::getCity, city);
        }
        if (!StringUtils.isEmpty(keywords)) {
            queryWrapper.and((t) -> t.like(EnterpriseInfo::getEnterpriseName, keywords)
                    .or()
                    .like(EnterpriseInfo::getCity, keywords));
        }
        //排序方式(排序值升序)
        if (orderBy == null) {
        } else if (orderBy == PublicEnum.ORDER_BY_SORT.getCode()) {
            queryWrapper.orderByDesc(EnterpriseInfo::getSort).orderByDesc(EnterpriseInfo::getGmtCreate);
            //排序方式(修改时间降序)
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_MODIFIED.getCode()) {
            queryWrapper.orderByDesc(EnterpriseInfo::getGmtModified);
            //排序方式(创建时间降序)
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_CREATE.getCode()) {
            queryWrapper.orderByDesc(EnterpriseInfo::getGmtCreate);
        }
        queryWrapper.ne(EnterpriseInfo::getEnterpriseType, 2);
        IPage<EnterpriseInfo> page = this.page(
                new Query<EnterpriseInfo>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }
    @Override
    public PageUtils outPurchaserLedger(JSONObject jsonObject) {
        LambdaQueryWrapper<EnterpriseInfo> queryWrapper = new LambdaQueryWrapper<>();
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String nickName = (String) innerMap.get("nickName");
        String name = (String) innerMap.get("name");
        String phone = (String) innerMap.get("phone");
        // TODO 需要改为正确的条件
        queryWrapper.isNull(EnterpriseInfo::getSupplierType);//贸易商
        queryWrapper.eq(EnterpriseInfo::getIsDelete, 0);//非删除
        queryWrapper.eq(EnterpriseInfo::getIsSupplier, 2);//是供应商
        // 高级搜索
        if (!StringUtils.isEmpty(nickName)) {
            queryWrapper.like(EnterpriseInfo::getAdminName, nickName);
        }
        if (!StringUtils.isEmpty(name)) {
            queryWrapper.like(EnterpriseInfo::getAdminName, name);
        }
        if (!StringUtils.isEmpty(phone)) {
            queryWrapper.like(EnterpriseInfo::getAdminPhone, phone);
        }
        //排序方式(排序值升序)
        if (orderBy == null) {
        } else if (orderBy == PublicEnum.ORDER_BY_SORT.getCode()) {
            queryWrapper.orderByDesc(EnterpriseInfo::getSort).orderByDesc(EnterpriseInfo::getGmtCreate);
            //排序方式(修改时间降序)
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_MODIFIED.getCode()) {
            queryWrapper.orderByDesc(EnterpriseInfo::getGmtModified);
            //排序方式(创建时间降序)
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_CREATE.getCode()) {
            queryWrapper.orderByDesc(EnterpriseInfo::getGmtCreate);
        }
        queryWrapper.ne(EnterpriseInfo::getEnterpriseType, 2);
        IPage<EnterpriseInfo> page = this.page(
                new Query<EnterpriseInfo>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }
    /*@Override
    public PageUtils listByEntityLedger(JSONObject jsonObject, LambdaQueryWrapper<EnterpriseInfo> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String enterpriseName = (String) innerMap.get("enterpriseName");
        Integer enterpriseType = (Integer) innerMap.get("enterpriseType");
        Integer state = (Integer) innerMap.get("state");
        String startDate = (String) innerMap.get("startDate");
        String endDate = (String) innerMap.get("endDate");
        Boolean interiorId = (Boolean) innerMap.get("interiorId");
        Integer isSupplier = (Integer) innerMap.get("isSupplier");
        Integer isPcwp = (Integer) innerMap.get("isPcwp");
        Integer mallType = (Integer) innerMap.get("mallType");
        String city = (String) innerMap.get("city");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        String keywords = (String) innerMap.get("keywords");
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        if (mallType == 1) {
            queryWrapper.eq(EnterpriseInfo::getIsDeviceMall, 1);
        }
        if (mallType == 0) {
            queryWrapper.eq(EnterpriseInfo::getIsMaterialMall, 1);
        }
        if (!StringUtils.isEmpty(enterpriseName)) {
            queryWrapper.like(EnterpriseInfo::getEnterpriseName, enterpriseName);
        }
        if (!StringUtils.isEmpty(interiorId)) {
            if (interiorId) {
                queryWrapper.isNotNull(EnterpriseInfo::getInteriorId);
            } else {
                queryWrapper.isNull(EnterpriseInfo::getInteriorId);
            }
        }
        if (enterpriseType != null) {
            queryWrapper.eq(EnterpriseInfo::getEnterpriseType, enterpriseType);
        }
        queryWrapper.between(org.apache.commons.lang.StringUtils.isNotEmpty(startDate) && org.apache.commons.lang.StringUtils.isNotEmpty(endDate), EnterpriseInfo::getGmtCreate, startDate, endDate);
        if (isSupplier != null) {
            queryWrapper.eq(EnterpriseInfo::getIsSupplier, isSupplier);
        }
        if (isPcwp != null) {
            queryWrapper.eq(EnterpriseInfo::getIsPcwp, isPcwp);
        }
        if (state != null) {
            queryWrapper.eq(EnterpriseInfo::getState, state);
        }
        if (!StringUtils.isEmpty(city)) {
            queryWrapper.like(EnterpriseInfo::getCity, city);
        }
        if (!StringUtils.isEmpty(keywords)) {
            queryWrapper.and((t) -> t.like(EnterpriseInfo::getEnterpriseName, keywords)
                    .or()
                    .like(EnterpriseInfo::getCity, keywords));
        }
        //排序方式(排序值升序)
        if (orderBy == null) {
        } else if (orderBy == PublicEnum.ORDER_BY_SORT.getCode()) {
            queryWrapper.orderByDesc(EnterpriseInfo::getSort).orderByDesc(EnterpriseInfo::getGmtCreate);
            //排序方式(修改时间降序)
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_MODIFIED.getCode()) {
            queryWrapper.orderByDesc(EnterpriseInfo::getGmtModified);
            //排序方式(创建时间降序)
        } else if (orderBy == PublicEnum.ORDER_BY_GMT_CREATE.getCode()) {
            queryWrapper.orderByDesc(EnterpriseInfo::getGmtCreate);
        }
        queryWrapper.ne(EnterpriseInfo::getEnterpriseType, 2);
        IPage<EnterpriseInfo> page = this.page(
                new Query<EnterpriseInfo>().getPage(jsonObject),
                queryWrapper
        );
        List<EnterpriseInfo> list = page.getRecords();
        List<EnterpriseLedgerVo> enterpriseLedgerVos = new ArrayList<>();
        if(!list.isEmpty()){
            for (EnterpriseInfo enterpriseInfo : list){
                EnterpriseLedgerVo enterpriseLedgerVo = new EnterpriseLedgerVo();
                enterpriseLedgerVo.setEnterpriseId(enterpriseInfo.getEnterpriseId());
                enterpriseLedgerVo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                // 获取店铺信息
                Shop byEnterpriseId = shopMapper.findByEnterpriseId(enterpriseInfo.getEnterpriseId());
                if(byEnterpriseId != null){
                    enterpriseLedgerVo.setShopId(byEnterpriseId.getShopId());
                    enterpriseLedgerVo.setShopName(byEnterpriseId.getShopName());
                }
                // 获取合同信息
                Contract byPartyBOrgId = contractService.getByPartyBOrgId(enterpriseInfo.getEnterpriseId());
                if(byPartyBOrgId != null){
                    enterpriseLedgerVo.setAgreementNo(byPartyBOrgId.getContractNo());
                }
                // 获取年费缴费信息
                PlatformYearFee platformYearFee = platformYearFeeService.getByEnterpriseId(enterpriseInfo.getEnterpriseId());

                if(platformYearFee != null){
                    enterpriseLedgerVo.setAnnuaFeeCycle(platformYearFee.getGmtCreate());
                    enterpriseLedgerVo.setServeEndTime(platformYearFee.getServeEndTime());
                }
                // 获取交易缴费信息
                List<PlatformDealFeeDtl> byEnterpriseId1 = platformDealFeeDtlService.getByEnterpriseId(enterpriseInfo.getEnterpriseId());
                if(!byEnterpriseId1.isEmpty()){
                    enterpriseLedgerVo.setTotalDealAmount(byEnterpriseId1.stream().map(PlatformDealFeeDtl::getDealAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    enterpriseLedgerVo.setTotalDealFee(byEnterpriseId1.stream().map(PlatformDealFeeDtl::getPayFee).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
                enterpriseLedgerVos.add(enterpriseLedgerVo);
            }
        }
        // 封装成台账
        IPage<EnterpriseLedgerVo> resultPage = new Page<>();
        resultPage.setCurrent(page.getCurrent());
        resultPage.setSize(page.getSize());
        resultPage.setTotal(page.getTotal());
        resultPage.setPages(page.getPages());
        resultPage.setRecords(enterpriseLedgerVos);
        return new PageUtils(resultPage);
    }*/

    @Override
    public PageUtils listByEntityLedger(JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String enterpriseName = (String) innerMap.get("enterpriseName");
        String contractNo = (String) innerMap.get("contractNo");
        int count = baseMapper.ledgerListCount(jsonObject.getInnerMap());
        pageUtils.pageDispose(jsonObject, count);
        Page<EnterpriseLedgerVo> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<EnterpriseLedgerVo> vos = baseMapper.ledgerList(pages, jsonObject);
        pages.setRecords(vos);
        return new PageUtils(pages);
    }

    @Override
    public EnterpriseInfo findOuterByCreditCode(String socialCreditCode) {
        QueryWrapper queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("social_credit_code", socialCreditCode);
        EnterpriseInfo enterpriseInfoMap = enterpriseInfoService.getOne(queryWrapper);
        return enterpriseInfoMap;
    }

    /**
     * 根据登陆用户获取当前企业
     *
     * @return
     */
    @Override
    public EnterpriseInfo getInfo(String enterpriseId, Integer mallType, String shopId) {
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        LambdaQueryChainWrapper<EnterpriseInfo> q = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, enterpriseId);
        if (mallType == PublicEnum.MATERIALS.getCode()) {
            q.eq(EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode());
        } else {
            q.eq(EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode());
        }
        EnterpriseInfo enterpriseInfo = q.one();
        if (enterpriseInfo != null) {
            List<File> list = fileService.lambdaQuery()
                    .eq(File::getRelevanceId, shopId)
                    .eq(File::getRelevanceType, 4).list();
            enterpriseInfo.setFiles(list);
        }
        return enterpriseInfo;
    }

    /**
     * 修改企业
     *
     * @param enterpriseInfo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEnterprise(EnterpriseInfo enterpriseInfo) {
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        User user = userService.getById(userId);
        if (user != null && org.apache.commons.lang.StringUtils.isBlank(user.getRealName())) {
            userService.lambdaUpdate().eq(User::getUserId, userId)
                    .set(User::getRealName, enterpriseInfo.getAdminName()).update();
        }
        EnterpriseInfo info = baseMapper.selectById(enterpriseInfo.getEnterpriseId());
        if (info.getIsNoSupplierAudit() == 1) {
            enterpriseInfo.setIsNoSupplierAudit(0);
            enterpriseInfo.setIsSupplier(1);
        }
        enterpriseInfo.setIsSupplier(1);//企业个体户修改完信息后需要管理员重新审核
        enterpriseInfo.setCertificateType(String.join(",",enterpriseInfo.getCertificate()));
        enterpriseInfo.setFileModifyTime(new DateTime());
        boolean b = updateById(enterpriseInfo);
        if (b) {
            List<File> files = enterpriseInfo.getFiles();
            if (files != null && files.size() > 0) {
                fileService.updateBatchFileByEnterPriseInfo(enterpriseInfo);
            }
            if (!CollectionUtils.isEmpty(enterpriseInfo.getEpLists())){
                List<EnterprisePerformance> epLists = enterpriseInfo.getEpLists();
                for(EnterprisePerformance ep : epLists) {
                    if(ep.getEnterprisePerformanceId() != null && !ep.getEnterprisePerformanceId().isEmpty()){//更新
                        ep.setSupplyStartDate(ep.getGhdate().size()==2 ? ep.getGhdate().get(0):null);
                        ep.setSupplyEndDate(ep.getGhdate().size()==2 ? ep.getGhdate().get(1):null);
                        ep.setGmtModified(new DateTime());
                        enterprisePerformanceService.updateById(ep);
                    }else{//添加
                        ep.setEnterpriseId(enterpriseInfo.getEnterpriseId());
                        ep.setSupplyStartDate(ep.getGhdate().size()==2 ? ep.getGhdate().get(0):null);
                        ep.setSupplyEndDate(ep.getGhdate().size()==2 ? ep.getGhdate().get(1):null);
                        ep.setStatus(1);
                        ep.setGmtCreate(new DateTime());
                        ep.setIsDelete(0);
                        enterprisePerformanceService.save(ep);
                    }
                }
            }
        }

    }

    /**
     * 成为企业
     *
     * @param enterpriseInfo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void becomeEnterprise(EnterpriseInfo enterpriseInfo) {
        User userById = userService.getById(ThreadLocalUtil.getCurrentUser().getUserId());
        EnterpriseInfo enterpriseInfo1 = lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, userById.getEnterpriseId())
                .select(EnterpriseInfo::getEnterpriseType).one();
        if (enterpriseInfo1.getEnterpriseType() == 0 || enterpriseInfo1.getEnterpriseType() == 1) {
            throw new BusinessException(400, "用户已成为企业或个体户！");
        }
        LambdaQueryChainWrapper<EnterpriseInfo> q = lambdaQuery().and(t -> {
            t.eq(EnterpriseInfo::getSocialCreditCode, enterpriseInfo.getSocialCreditCode())
                    .or()
                    .eq(EnterpriseInfo::getEnterpriseName, enterpriseInfo.getEnterpriseName());
        });
        EnterpriseInfo outerByCreditCode = q.one();
        if (outerByCreditCode != null) {
            throw new BusinessException(400, "该企业已注册！");
        }
        Integer mallType = enterpriseInfo.getMallType();
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        if (mallType == PublicEnum.MATERIALS.getCode()) {
            enterpriseInfo.setIsMaterialMall(PublicEnum.IS_YES.getCode());
            enterpriseInfo.setIsDeviceMall(PublicEnum.IS_NO.getCode());
        } else {
            enterpriseInfo.setIsMaterialMall(PublicEnum.IS_NO.getCode());
            enterpriseInfo.setIsDeviceMall(PublicEnum.IS_YES.getCode());
        }
        // 发送请求查询是否供应商

        enterpriseInfo.setIsSupplier(1);
        String url2 = mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.IS_PCWP
                + enterpriseInfo.getSocialCreditCode();
        HttpHeaders headers2 = new HttpHeaders();
        headers2.add("token", mallConfig.thirdApiToken);
        R isSupp = restTemplateUtils.get(url2, headers2, R.class).getBody();
        if (isSupp.getCode() != null && isSupp.getCode() == 200) {
            if (isSupp.getData() != null) {
                // 有信息也是待定状态，需要后台进行审核
                enterpriseInfo.setIsPcwp(1);
            } else {
                enterpriseInfo.setIsPcwp(0);
            }
        } else {
            log.error("供应商错误：" + isSupp);
            throw new BusinessException(400, "远程供应商接口错误！");
        }
//        enterpriseInfo.setCreationTime(new Date());
        boolean b = updateById(enterpriseInfo);

        if (b) {
            List<File> files = enterpriseInfo.getFiles();
            if (files != null && files.size() > 0) {
                fileService.updateBatchFileByEnterPriseInfo(enterpriseInfo);
            }
            String userId = ThreadLocalUtil.getCurrentUser().getUserId();
            User user = new User();
            user.setUserId(userId);
            user.setRealName(enterpriseInfo.getAdminName());
            userService.updateById(user);
        }
    }

    /**
     * 修改如果有店铺修改店铺
     *
     * @param enterpriseInfos
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchAndShopById(List<EnterpriseInfo> enterpriseInfos) {
        for (EnterpriseInfo enterpriseInfo : enterpriseInfos) {
            //重新审核供应商
            if (enterpriseInfo.getIsSupplier() == 0) {
                Shop shop = shopService.lambdaQuery().eq(Shop::getMallType, mallConfig.mallType)
                        .eq(Shop::getEnterpriseId, enterpriseInfo.getEnterpriseId()).one();
                if (shop != null) {
                    shop.setState(0);
                    shopService.updateById(shop);
                }
            }
            updateById(enterpriseInfo);
        }


    }

    @Override
    public Integer getOrgIsSupperCountById(String enterpriseId, Integer isSupplier) {
        Integer count = enterpriseInfoService.lambdaQuery()
                .eq(EnterpriseInfo::getEnterpriseId, enterpriseId)
                .eq(EnterpriseInfo::getIsSupplier, isSupplier).count();
        return count;
    }

    /**
     * 根据企业id获取企业信息好附件信息
     *
     * @param enterpriseId
     * @return
     */
    @Override
    public EnterpriseInfo getEnterInfoAndFileList(String enterpriseId) {

        return null;
    }

    /**
     * 根据企业id获取附件列表
     *
     * @param enterpriseId
     * @return
     */
    @Override
    public List<File> getEnterFileList(String enterpriseId) {
        List<File> list = fileService.lambdaQuery()
                .eq(File::getRelevanceId, enterpriseId)
                .eq(File::getRelevanceType, 6).list();
        return list;
    }

    /**
     * 导入供应商及诶扣
     *
     * @param file
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ImportSupplerExcelResultVO> uploadSupplierExcelFile(MultipartFile file) {
        EnterpriseInfoService enterpriseInfoService = SpringBeanUtil.getBean(EnterpriseInfoService.class);
        try {
            ArrayList<ImportSupplerExcelResultVO> vos = new ArrayList<>();
            List<Supplier> objects = EasyExcelUtils.readExcelOneSheet(file.getInputStream(), Supplier.class);
            for (Supplier supplier : objects) {
                ImportSupplerExcelResultVO vo = new ImportSupplerExcelResultVO();
                vo.setEnterpriseName(supplier.getEnterpriseName());
                vo.setSocialCreditCode(supplier.getSocialCreditCode());
                vo.setLegalRepresentative(supplier.getLegalRepresentative());
                vo.setAdminPhone(supplier.getAdminPhone());
                vo.setState("成功");
                try {
                    enterpriseInfoService.saveImportSupplier(supplier);
                } catch (Exception e) {
                    vo.setState("失败");
                    vo.setFail(e.getMessage());
                    log.error("导入失败：" + supplier.getEnterpriseName() + "原因：" + e.getMessage());
                    ImportSuppler importSuppler = new ImportSuppler();
                    importSuppler.setEnterpriseName(supplier.getEnterpriseName());
                    importSuppler.setSocialCreditCode(supplier.getSocialCreditCode());
                    importSuppler.setLegalRepresentative(supplier.getLegalRepresentative());
                    importSuppler.setAdminPhone(supplier.getAdminPhone());
                    importSuppler.setState("失败");
                    importSuppler.setFail(e.getMessage() + "");
                    importSupplerService.save(importSuppler);
                }
                vos.add(vo);
            }
            return vos;
        } catch (IOException e) {
            log.error("导入供应商异常信息：" + e.getMessage());
        }
        return new ArrayList<>();
    }

    /**
     * excel导入供应商
     *
     * @param supplier
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveImportSupplier(Supplier supplier) {
        if (StringUtils.isEmpty(supplier.getEnterpriseName())) {
            throw new BusinessException(400, "未填入供应商名称！");
        }
        if (StringUtils.isEmpty(supplier.getSocialCreditCode())) {
            throw new BusinessException(400, "未填入供应商的信用代码！");
        }
        if (StringUtils.isEmpty(supplier.getAdminPhone())) {
            throw new BusinessException(400, "未填入联系电话！");
        }
        if (StringUtils.isEmpty(supplier.getLegalRepresentative())) {
            throw new BusinessException(400, "未填入法定代表人！");
        }
        String phone = supplier.getAdminPhone();
        User oneUser = userService.lambdaQuery().eq(User::getUserMobile, supplier.getAdminPhone()).one();
        if (oneUser != null) {
            Integer isMaterial = oneUser.getIsMaterial();
            Integer isDevice = oneUser.getIsDevice();
            if (isMaterial == 1 && isDevice == 1) {
                throw new BusinessException(400, "手机号已注册！");
            }
            if (isMaterial == 1) {
                throw new BusinessException(400, "手机号已在物资商城注册！");
            }
            if (isDevice == 1) {
                throw new BusinessException(400, "手机号已在装备商城注册！");
            }
        }
        //通过信用代码查询是否已注册（外部）
        LambdaQueryChainWrapper<EnterpriseInfo> q = lambdaQuery().and(t -> {
            t.eq(EnterpriseInfo::getSocialCreditCode, supplier.getSocialCreditCode())
                    .or()
                    .eq(EnterpriseInfo::getEnterpriseName, supplier.getEnterpriseName());
        });
        EnterpriseInfo outerByCreditCode = q.one();
        if (outerByCreditCode == null) {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            HashMap<String, Object> map = new HashMap<>();
            HashMap<String, String> userMap = new HashMap<>();
            HashMap<String, Object> orgMap = new HashMap<>();
            userMap.put("phoneNo", phone);
            userMap.put("userName", supplier.getLegalRepresentative());
            userMap.put("adminNumber", supplier.getAdminNumber());
            // 默认密码就是手机号
            String encryptPhone = AESUtil.encrypt(phone);
            userMap.put("password", encryptPhone);
            //机构类型(1:个人|2:个体户|3:企业，，，远程)
            // 都是企业
            orgMap.put("orgType", 3);
            orgMap.put("orgName", supplier.getEnterpriseName());
            map.put("user", userMap);
            map.put("org", orgMap);
            map.put("sysCode", "msp");
            String content = JSON.toJSONString(map);
            HttpEntity<String> request = new HttpEntity<>(content, headers);
            // 调用远程进行注册
            String url = mallConfig.prodPcwp2Url + "/identity/auth/userorg/signup";
            R<Map> r = restTemplate.postForObject(url, request, R.class);
            if (r.getCode() == 200) {
                EnterpriseInfo enterpriseInfo = new EnterpriseInfo();
                User user = new User();
                BeanUtils.copyProperties(supplier, enterpriseInfo);
                //处理地址
                String detailedAddress = supplier.getDetailedAddress();
                if (!StringUtils.isEmpty(detailedAddress)) {
                    String[] split = detailedAddress.split("-");
                    if (split.length >= 4) {
                        enterpriseInfo.setProvinces(split[1]);
                        enterpriseInfo.setCity(split[2]);
                        enterpriseInfo.setCounty(split[3]);
                    }
                }
                // 企业编号
                enterpriseInfo.setEnterpriseNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_QY.getRemark()));
                // 哪个商城企业
                enterpriseInfo.setIsDeviceMall(PublicEnum.IS_YES.getCode());
                user.setIsDevice(PublicEnum.IS_YES.getCode());
                // 营业状态
//                enterpriseInfo.setCreationTime(new Date());
                enterpriseInfo.setAdminName(supplier.getLegalRepresentative());
                enterpriseInfo.setEnterpriseBusinessType(PublicEnum.IS_YES.getCode());
                enterpriseInfo.setIsSupplier(2);
                enterpriseInfo.setEnterpriseType(1);
                enterpriseInfo.setImportType(1);
                // 状态
                enterpriseInfo.setState(PublicEnum.IS_YES.getCode());
                // 办理状态
                enterpriseInfo.setHandlingResult(PublicEnum.IS_YES.getCode());
                //存入企业附加信息
                int insert = baseMapper.insert(enterpriseInfo);
                if (insert > 0) {
                    //存入user表
                    user.setNickName(supplier.getLegalRepresentative());
                    user.setAccount(phone);
                    user.setUserNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_YH.getRemark()));
                    user.setEnterpriseId(enterpriseInfo.getEnterpriseId());
                    user.setUserMobile(phone);
                    user.setPassword(encryptPhone);
                    user.setIsAdmin(UserEnum.IS_ADMIN_YES.getCode());
                    user.setRealName(enterpriseInfo.getAdminName());
                    // 默认昵称就是姓名
                    user.setRealName(supplier.getLegalRepresentative());
                    // 状态
                    user.setIsInternalUser(PublicEnum.IS_NO.getCode());
                    user.setPlatformAdmin(PublicEnum.IS_NO.getCode());
                    user.setState(1);
                    boolean save = userService.save(user);
                    if (!save) {
                        log.error("用户注册失败，记录日志：" + user);
                        throw new BusinessException(400, "用户注册失败！");
                    }
                } else {
                    log.error("企业注册失败，记录日志：" + enterpriseInfo);
                    throw new BusinessException(400, "企业注册失败！");
                }
            } else {
                throw new BusinessException(r.getCode(), r.getMessage());
            }
        } else {
            Integer isMaterial = outerByCreditCode.getIsMaterialMall();
            Integer isDevice = outerByCreditCode.getIsDeviceMall();
            if (isMaterial == 1 && isDevice == 1) {
                throw new BusinessException(400, "供应商已注册！");
            }
            if (isMaterial == 1) {
                throw new BusinessException(400, "供应商已在物资商城注册！");
            }
            if (isDevice == 1) {
                throw new BusinessException(400, "供应商已在装备商城注册！");
            }
            throw new BusinessException(400, "供应商已注册！");
        }
    }

    @Override
    public void create(EnterpriseInfo enterpriseInfo) {
        //调用父类方法即可
        //也可以baseMapper.insert
        boolean save = super.save(enterpriseInfo);
        if (!save) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "操作失败！");
        }
    }

    @Override
    public void updateIsSupplier(List<String> ids, String s) {
        List<EnterpriseInfo> resutls = listByIds(ids);
        for (EnterpriseInfo enterpriseInfo : resutls) {
            if ("1".equals(s)) {
                enterpriseInfo.setIsSupplier(EnterpriseEnum.IS_SUPPLIER_YES.getCode());
                enterpriseInfo.setState(PublicEnum.STATE_OPEN.getCode());
            } else {
                enterpriseInfo.setIsSupplier(EnterpriseEnum.IS_SUPPLIER_NO.getCode());
                enterpriseInfo.setState(PublicEnum.STATE_STOP.getCode());
            }
        }
        super.saveOrUpdateBatch(resutls);
    }


    @Override
    public EnterpriseInfo getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Value("${app.verify-code: true}")
    private boolean verifyCode;
    /**
     * 企业用户注册、个体户注册（外部）
     *
     * @param enterpriseInfo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R enterpriseRegistration(EnterpriseInfo enterpriseInfo) {
//        if(PasswordUtils.password(enterpriseInfo.getAdminPassword())) {
//            throw new BusinessException("密码中必须包含字母、数字、特称字符，至少8个字符，最多20个字符");
//        }
        Integer mallType = enterpriseInfo.getMallType();
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        // 校验税率
//        BigDecimal taxTate = enterpriseInfo.getTaxRate();
//        if (ObjectUtils.isEmpty(taxTate)) {
//            throw new BusinessException("请输入税率（百分比）");
//        } else {
//            if (taxTate.compareTo(new BigDecimal(0)) < 0 || taxTate.compareTo(new BigDecimal(100)) > 0) {
//                throw new BusinessException("税率超出限制");
//            }
//        }
        String code = enterpriseInfo.getVerificationCode();
        String phone = enterpriseInfo.getAdminPhone();
        // 校验手机号
        if (RegexUtils.isPhoneInvalid(phone)) {
            throw new BusinessException(PublicEnum.RESULT_CODE_500200.getCode(), "手机号码格式错误！");
        }
        if (verifyCode) {
            if (org.apache.commons.lang.StringUtils.isBlank(code)) {
                throw new BusinessException(PublicEnum.RESULT_CODE_500200.getCode(), "请输入验证码！");
            }
            String rCode = stringRedisTemplate.opsForValue().get(RedisKey.REGISTER_CODE_KEY + phone);
            if (rCode == null) {
                throw new BusinessException(PublicEnum.RESULT_CODE_500200.getCode(), "验证码已失效，请重新发送！");
            }
            if (!code.equals(rCode.split("_")[0])) {
                throw new BusinessException(PublicEnum.RESULT_CODE_500200.getCode(), "验证码错误！");
            }
        }
        Integer count = userService.lambdaQuery().eq(User::getUserMobile, enterpriseInfo.getAdminPhone()).count();
        if (count > 0) {
            return R.failed(PublicEnum.RESULT_CODE_500200.getCode(), "用户已经存在！");
        }
        Integer count1 = lambdaQuery().eq(EnterpriseInfo::getAdminPhone, enterpriseInfo.getAdminPhone()).count();
        if (count1 > 0) {
            return R.failed(PublicEnum.RESULT_CODE_500200.getCode(), "管理员手机号已被其他人绑定!");
        }
        User oneUser = userService.lambdaQuery().eq(User::getUserMobile, enterpriseInfo.getAdminPhone()).one();
        if (oneUser != null && oneUser.getIsMaterial() != null && oneUser.getIsMaterial() == 1) {
            return R.failed("手机号已在物资商城注册！可直接登录！");
        } else if (oneUser != null && oneUser.getIsDevice() != null && oneUser.getIsDevice() == 1) {
            return R.failed("手机号已在装备商城注册！可直接登录！");
        } else if (oneUser != null) {
            return R.failed("手机号已注册！可直接登录！");
        }
        //通过信用代码查询是否已注册（外部）
        LambdaQueryChainWrapper<EnterpriseInfo> q = lambdaQuery().and(t -> {
            t.eq(EnterpriseInfo::getSocialCreditCode, enterpriseInfo.getSocialCreditCode())
                    .or()
                    .eq(EnterpriseInfo::getEnterpriseName, enterpriseInfo.getEnterpriseName());
        });
        EnterpriseInfo outerByCreditCode = q.one();
        if (outerByCreditCode == null) {
            // 发送请求查询是否供应商
            PcwpRes<SupplierRes> isSupp = pcwpService.getSupplierByCreditCode(enterpriseInfo.getSocialCreditCode(), mallConfig.thirdApiToken);
            enterpriseInfo.setIsSupplier(1);
            if (isSupp.getCode() != null && isSupp.getCode() == 200) {
                if (isSupp.getData() != null) {
                    // 有信息也是待定状态，需要后台进行审核
                    enterpriseInfo.setIsPcwp(1);
                } else {
                    enterpriseInfo.setIsPcwp(0);
                }
            } else {
                log.error("供应商查询返回：" + isSupp);
                throw new BusinessException(isSupp.getCode(), isSupp.getMessage());
            }
            SignUp.UserSignUp userSignUp = new SignUp.UserSignUp();
            userSignUp.setPassword(enterpriseInfo.getAdminPassword());
            userSignUp.setUserName(enterpriseInfo.getAdminName());
            userSignUp.setAdminNumber(enterpriseInfo.getAdminNumber());
            userSignUp.setPhoneNo(phone);

            SignUp.OrgSignUp orgSignUp = new SignUp.OrgSignUp();
            //	机构类型(1:个人|2:个体户|3:企业，，，远程)
            if (enterpriseInfo.getEnterpriseType() == 1) {
                orgSignUp.setOrgType(3);
            }
            if (enterpriseInfo.getEnterpriseType() == 0) {
                orgSignUp.setOrgType(2);
            }
            orgSignUp.setOrgName(enterpriseInfo.getEnterpriseName());

            SignUp signUp = new SignUp();
            signUp.setSysCode("msp");
            signUp.setOrg(orgSignUp);
            signUp.setUser(userSignUp);
            PcwpRes<Boolean> r = pcwpService.userOrgSignUp(signUp);
            if (r.getCode() == 200) {
                // 企业编号
                enterpriseInfo.setEnterpriseNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_QY.getRemark()));
                User user = new User();
                // 哪个商城企业
                if (mallType == PublicEnum.MATERIALS.getCode()) {
                    enterpriseInfo.setIsMaterialMall(PublicEnum.IS_YES.getCode());
                    user.setIsMaterial(PublicEnum.IS_YES.getCode());
                } else {
                    enterpriseInfo.setIsDeviceMall(PublicEnum.IS_YES.getCode());
                    user.setIsDevice(PublicEnum.IS_YES.getCode());
                }
                // 营业状态
                enterpriseInfo.setEnterpriseBusinessType(PublicEnum.IS_YES.getCode());
                // 状态
                enterpriseInfo.setState(PublicEnum.IS_YES.getCode());
                // 办理状态
                enterpriseInfo.setHandlingResult(PublicEnum.IS_YES.getCode());
                enterpriseInfo.setCertificateType(String.join(",",enterpriseInfo.getCertificate()));
                //存入企业附加信息
                // 这里通过注册入库 interiorId没有值
                int insert = baseMapper.insert(enterpriseInfo);
                if (insert > 0) {
                    //存入user表
                    user.setNickName(user.getRealName());
                    user.setAccount(enterpriseInfo.getAdminPhone());
                    user.setUserNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_YH.getRemark()));
                    user.setEnterpriseId(enterpriseInfo.getEnterpriseId());
                    user.setUserMobile(enterpriseInfo.getAdminPhone());
                    user.setPassword(enterpriseInfo.getAdminPassword());
                    user.setIsAdmin(UserEnum.IS_ADMIN_YES.getCode());
                    user.setRealName(enterpriseInfo.getAdminName());
                    // 默认昵称就是姓名
                    user.setNickName(enterpriseInfo.getAdminName());
                    // 状态
                    user.setIsInternalUser(PublicEnum.IS_NO.getCode());
                    user.setPlatformAdmin(PublicEnum.IS_NO.getCode());
                    user.setState(1);
                    boolean save = userService.save(user);
                    if (save) {
                        if (!CollectionUtils.isEmpty(enterpriseInfo.getFiles())) {
                            List<File> files = enterpriseInfo.getFiles();
                            for (File file : files) {
                                file.setRelevanceId(enterpriseInfo.getEnterpriseId());
                                file.setMallType(enterpriseInfo.getMallType());
                                file.setRelevanceType(8);
                                switch (file.getCategory()){
                                    case "营业执照":
                                        file.setStartTime(enterpriseInfo.getCreationTime());
                                        file.setEndTime(enterpriseInfo.getLicenseTerm());
                                        break;
                                    case "法定代表人身份证人像面照": case "法定代表人身份证国徽面照": case "法定代表人身份证明":
                                        file.setStartTime(enterpriseInfo.getLpStartTime());
                                        file.setEndTime(enterpriseInfo.getLpEndTime());
                                        break;
                                    case "其他证书":
                                        file.setStartTime(enterpriseInfo.getOtherStartTime());
                                        file.setEndTime(enterpriseInfo.getOtherEndTime());
                                        break;
                                    case "资质证书":
                                        file.setStartTime(enterpriseInfo.getQcStartTime());
                                        file.setEndTime(enterpriseInfo.getQcEndTime());
                                        break;
                                    case "中国执行信息公开网查询情况":
                                        file.setStartTime(enterpriseInfo.getZxgkStartTime());
                                        file.setEndTime(enterpriseInfo.getZxgkEndTime());
                                        break;
                                    case "信用中国报告":
                                        file.setStartTime(enterpriseInfo.getCcrStartTime());
                                        file.setEndTime(enterpriseInfo.getCcrEndTime());
                                        break;
                                    case "税务评级证明":
                                        file.setStartTime(enterpriseInfo.getTrcStartTime());
                                        file.setEndTime(enterpriseInfo.getTrcEndTime());
                                        break;
                                    case "最近一期完税证明":
                                        file.setStartTime(enterpriseInfo.getTpcStartTime());
                                        file.setEndTime(enterpriseInfo.getTpcEndTime());
                                        break;
                                }
                            }
                            boolean b = fileService.saveBatch(files);
                            if (!b) {
                                throw new BusinessException(400, "附件资料保存失败！");
                            }
                        }
                        if (!CollectionUtils.isEmpty(enterpriseInfo.getEpLists())){
                            List<EnterprisePerformance> epLists = enterpriseInfo.getEpLists();
                            for(EnterprisePerformance ep : epLists) {
                                ep.setEnterpriseId(enterpriseInfo.getEnterpriseId());
                                ep.setSupplyStartDate(ep.getGhdate().size()==2 ? ep.getGhdate().get(0):null);
                                ep.setSupplyEndDate(ep.getGhdate().size()==2 ? ep.getGhdate().get(1):null);
                                ep.setStatus(1);
                                ep.setGmtCreate(new DateTime());
                                ep.setIsDelete(0);
                            }
                            enterprisePerformanceService.saveBatch(epLists);
                        }
                        return R.success();
                    } else {
                        return R.failed(400, "注册失败");
                    }
                } else {
                    return R.failed(400, "注册失败");
                }
            } else {
                throw new BusinessException(r.getCode(), r.getMessage());
            }
        } else {
            if (outerByCreditCode.getInteriorId() != null) {
                return R.failed(400, "该企业属于内部企业不能注册，请联系管理员！");
            } else {
                return R.failed(400, "该企业已注册！");
            }
        }
    }

    @Override
    public EnterpriseInfo getAuditState(String enterpriseId) {
        LambdaQueryWrapper<EnterpriseInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseInfo::getEnterpriseId, enterpriseId);
        if (mallConfig.mallType == 0) {
            wrapper.eq(EnterpriseInfo::getIsMaterialMall, 1);
        } else if (mallConfig.mallType == 1) {
            wrapper.eq(EnterpriseInfo::getIsDeviceMall, 1);
        }
        wrapper.select(EnterpriseInfo::getIsNoSupplierAudit, EnterpriseInfo::getIsSupplier, EnterpriseInfo::getAuditFailReason, EnterpriseInfo::getInteriorId);
        EnterpriseInfo one = getOne(wrapper);
        return one;
    }

    @Override
    public Boolean findSupperByCreditCodeAndType(String creditCode, Integer supplierType) {
        LambdaQueryWrapper<EnterpriseInfo> wrapper = new LambdaQueryWrapper<>();
        if (supplierType == 0) {
            wrapper.eq(EnterpriseInfo::getInteriorId, creditCode);
        } else {
            wrapper.eq(EnterpriseInfo::getSocialCreditCode, creditCode);
        }
        int count = count(wrapper);
        if (count > 0) {
            return true;
        } else {
            return false;
        }

    }

    @Override
    public RegisterPcwpFileVo selectIsPcwpUserByCode(String socialCreditCode, String programaKey) {
        RegisterPcwpFileVo pcwpFileVo = new RegisterPcwpFileVo();
        LambdaQueryChainWrapper<EnterpriseInfo> q = lambdaQuery().and(t -> {
            t.eq(EnterpriseInfo::getSocialCreditCode, socialCreditCode);
        });
        EnterpriseInfo outerByCreditCode = q.one();
//        if (outerByCreditCode == null) {
        // 发送请求查询是否供应商
        String url2 = mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.IS_PCWP
                + socialCreditCode;
        HttpHeaders headers2 = new HttpHeaders();
        headers2.add("token", mallConfig.thirdApiToken);
        R isSupp = restTemplateUtils.get(url2, headers2, R.class).getBody();
        if (isSupp.getCode() != null && isSupp.getCode() == 200) {
            if (isSupp.getData() != null) {
                pcwpFileVo.setIsPcwoSupplier("isPcwpSupplier");
            } else {
                pcwpFileVo.setIsPcwoSupplier("unPcwpSupplier");
            }
        } else {
            log.error("供应商查询返回：" + isSupp);
            throw new BusinessException(isSupp.getCode(), isSupp.getMessage());
        }
        /**
         * 获取pcwp注册入库资料
         */
        List<File> files = fileService.listTypeAndPkeyAndPkeyTwo(5, programaKey, pcwpFileVo.getIsPcwoSupplier());
        pcwpFileVo.setFiles(files);
        return pcwpFileVo;

//    }else {
//            throw new BusinessException(400, "该企业已注册！");
//        }

    }

    @Override
    public EnterpriseInfo findByInteriorId(String interiorId) {
        LambdaQueryWrapper<EnterpriseInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EnterpriseInfo::getInteriorId, interiorId);
        EnterpriseInfo enterpriseInfo = getOne(wrapper);
        return enterpriseInfo;
    }

    @Override
    public void updateIsSupplierById(String id) {
        EnterpriseInfo byId = super.getById(id);
        // TODOCR 这里很奇怪，老代码，在供应商管理界面执行设置税率操作后也会调用这里的代码，外部供应商也会被设置为pcwp内部供应商
        byId.setIsSupplier(2);
        byId.setIsPcwp(1);
        updateById(byId);
    }

    @Override
    public void updateIspcwpState() {
        LambdaQueryWrapper<EnterpriseInfo> q = new LambdaQueryWrapper<>();
        q.isNull(EnterpriseInfo::getInteriorId)
                .eq(EnterpriseInfo::getIsPcwp, 0);
        List<EnterpriseInfo> list = list(q);
        List<EnterpriseInfo> enterpriseInfos = new ArrayList<>();
        for (EnterpriseInfo enterpriseInfo : list) {
            // 发送请求查询是否供应商
            String url2 = mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.IS_PCWP
                    + enterpriseInfo.getSocialCreditCode();
            HttpHeaders headers2 = new HttpHeaders();
            headers2.add("token", mallConfig.thirdApiToken);
            R isSupp = restTemplateUtils.get(url2, headers2, R.class).getBody();

            if (isSupp.getCode() != null && isSupp.getCode() == 200) {
                if (isSupp.getData() != null) {
                    // 有信息也是待定状态，需要后台进行审核
                    enterpriseInfo.setIsPcwp(1);
                    enterpriseInfos.add(enterpriseInfo);
                }
            } else {
                log.error("供应商错误：" + isSupp);
                throw new BusinessException(400, "远程供应商接口错误！");
            }

        }
        enterpriseInfoService.updateBatchById(enterpriseInfos);
    }

    @Override
    public List<EnterpriseInfo> selectListByEnterPriseName(String enterpriseName) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        List<String> supplierIdList = shopSupplierReleService.getSupplierIdByShopId(user.getShopId());
        supplierIdList.add(user.getEnterpriseId());
        LambdaQueryWrapper<EnterpriseInfo> q = new LambdaQueryWrapper<>();
        q.like(EnterpriseInfo::getEnterpriseName, enterpriseName)
                .eq(EnterpriseInfo::getState, 1)
                // 限制是供应商的
                .eq(EnterpriseInfo::getIsSupplier, 2)
                .orderByAsc(EnterpriseInfo::getSort).orderByDesc(EnterpriseInfo::getGmtCreate)
                .notIn(EnterpriseInfo::getEnterpriseId, supplierIdList)
                .last("LIMIT " + 20)
                .select(EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getTaxRate);
        List<EnterpriseInfo> list = list(q);
        return list;

    }

    @Override
    public PageUtils getEnterpriceList(JSONObject jsonObject, LambdaQueryWrapper<EnterpriseInfo> wrapper) {
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        LambdaQueryWrapper<ShopSupplierRele> q = new LambdaQueryWrapper<>();
        q.eq(ShopSupplierRele::getSupplierId, enterpriseId);
        IPage<ShopSupplierRele> page = shopSupplierReleService.page(
                new Query<ShopSupplierRele>().getPage(jsonObject), q
        );
        List<ShopSupplierRele> records = page.getRecords();
        ArrayList<EnterpriseInfo> enterpriseInfos = new ArrayList<>();
        for (ShopSupplierRele record : records) {
            EnterpriseInfo enterpriseInfo = shopService.getEnterpriseInfoByShopId(record.getShopId());
            enterpriseInfo.setShopId(record.getShopId());
            enterpriseInfos.add(enterpriseInfo);
        }
        PageUtils pageUtils = new PageUtils();
        pageUtils.setCurrPage(Math.toIntExact(page.getPages()));
        pageUtils.setCurrPage(Math.toIntExact(page.getCurrent()));
        pageUtils.setList(enterpriseInfos);

        return pageUtils;
    }

    @Override
    public void updateArrearageBatch(List<EnterpriseInfo> enterpriseInfos) {
        ArrayList<EnterpriseInfo> list = new ArrayList<>();
        if (enterpriseInfos.size() > 0) {
            for (EnterpriseInfo enterpriseInfo : enterpriseInfos) {
                EnterpriseInfo byId = getById(enterpriseInfo.getEnterpriseId());
                byId.setArrearage(enterpriseInfo.getArrearage());
//                byId.setArrearageData(enterpriseInfo.getArrearageData());
                list.add(byId);
            }
            updateBatchById(list);
        }


    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void enterpriseShuDaoStatus() {
        // 将企业表中的shuDaoFlag状态根据ShuDaoEnterprise中更新
        boolean update = enterpriseInfoService.lambdaUpdate().set(EnterpriseInfo::getShuDaoFlag, 0).update();
        if (!update) {
            throw new BusinessException("操作失败");
        }
        List<EnterpriseInfo> list = enterpriseInfoService.lambdaQuery().list();
        List<EnterpriseInfo> updatedList = new ArrayList<>();
        List<ShudaoEnterprise> shudaoEnterpriseList = shudaoEnterpriseService.lambdaQuery().list();
        for (EnterpriseInfo enterpriseInfo : list) {
            for (ShudaoEnterprise shudaoEnterprise : shudaoEnterpriseList) {
                if (!ObjectUtils.isEmpty(enterpriseInfo.getEnterpriseName()) && !ObjectUtils.isEmpty(shudaoEnterprise.getEnterpriseName())) {
                    if (enterpriseInfo.getEnterpriseName().equals(shudaoEnterprise.getEnterpriseName())) {
                        EnterpriseInfo enterpriseInfo1 = new EnterpriseInfo();
                        enterpriseInfo1.setEnterpriseId(enterpriseInfo.getEnterpriseId());
                        enterpriseInfo1.setShuDaoFlag(1);
                        updatedList.add(enterpriseInfo1);
                    }
                }

            }
        }
        if (!CollectionUtils.isEmpty(updatedList)) {
            boolean b = enterpriseInfoService.updateBatchById(updatedList);
            // 更新一次店铺蜀道状态
            for (EnterpriseInfo info : updatedList) {
                Shop one = shopService.lambdaQuery().eq(Shop::getEnterpriseId, info.getEnterpriseId()).one();
                if (!ObjectUtils.isEmpty(one)) {
                    one.setShuDaoFlag(info.getShuDaoFlag());
                    shopService.updateById(one);
                }
            }
            if (!b) {
                throw new BusinessException("操作失败");
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void updateBatchArrearage(EnterpriseArrearageVo vo) {
        ArrayList<EnterpriseInfo> list = new ArrayList<>();
        if (vo.getEnterpriseIds().size() > 0) {
            lambdaUpdate()
                    .in(EnterpriseInfo::getEnterpriseId, vo.getEnterpriseIds())
                    .set(EnterpriseInfo::getArrearageDateNum, vo.getArrearageDateNum())
                    .set(EnterpriseInfo::getArrearage, vo.getArrearage())
                    .set(EnterpriseInfo::getArrearageDateType, vo.getArrearageDateType())
                    .update();
        }
//        for (String enterpriseId : vo.getEnterpriseIds()) {
//            EnterpriseInfo byId = getById(enterpriseId);
//            byId.setArrearage(vo.getArrearage());
//            byId.setArrearageDateNum(vo.getArrearageDateNum());
//            byId.setArrearageDateType(vo.getArrearageDateType());
//            list.add(byId);
//        }

    }


    @Override
    public List<String> getLocalEnterpriseByOrgIds(List<String> orgIds) {
        if (!CollectionUtils.isEmpty(orgIds)) {
            List<EnterpriseInfo> list = lambdaQuery().in(EnterpriseInfo::getInteriorId, orgIds).select(EnterpriseInfo::getEnterpriseId).list();
            if (!CollectionUtils.isEmpty(list)) {
                List<String> enterpriseIds = list.stream().map(EnterpriseInfo::getEnterpriseId).collect(Collectors.toList());
                return enterpriseIds;
            }
        }
        return null;
    }


    @Override
    public JSONObject getShopProgress() {
        ShopStateVO shopStateVO = userService.getShopStateByUserId();
        return null;
    }


    @Override
    public BigDecimal getEnterpriseInfoTaxRate() {
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        String enterpriseId = currentUser.getEnterpriseId();
        EnterpriseInfo byId = lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, enterpriseId).select(EnterpriseInfo::getTaxRate).one();
        if (!ObjectUtils.isEmpty(byId)) {
            if (!ObjectUtils.isEmpty(byId.getTaxRate())) {
                return byId.getTaxRate();
            }
        }
        throw new BusinessException("企业税率信息不能为空");
    }

    @Override
    public void resetPwd(String id) {
        EnterpriseInfo enterpriseInfo = this.getById(id);
        if (enterpriseInfo.getInteriorId()!=null) {
            throw new BusinessException("只有外部用户能重置密码");
        }
        User user = userService.lambdaQuery().eq(User::getEnterpriseId, enterpriseInfo.getEnterpriseId()).one();
        if (user==null) {
            throw new BusinessException("该企业未关联用户");
        }
        // 外部用户的账号就是手机号
        // 可以用原密码登录，也可以调用使用手机号登录接口，这里选择使用原密码登录
        String phone = user.getAccount();
        String oldPwd = user.getPassword();
        PcwpRes<TokenRes> tRes = pcwpService.signIn(phone,oldPwd);
        if (tRes.getCode()!=200) {
            throw new BusinessException("系统异常，请稍后重试");
        }
        String token = tRes.getData().getToken();
        // 也可以使用 user.getInteriorId();
        String userId = tRes.getData().getUserId();

        PcwpRes<Void> res = pcwpService.changeUserPassword(userId,oldPwd,AESUtil.encrypt(resetPassword), token);
        if (res.getCode()!=200) {
            throw new BusinessException(res.getMessage());
        }
        boolean flag = userService.lambdaUpdate()
                        .eq(User::getUserId, user.getUserId())
                        .set(User::getPassword,AESUtil.encrypt(resetPassword))
                        .update();
        if (!flag) {
            throw new BusinessException("系统异常，请联系系统管理员");
        }
        
    }


}





