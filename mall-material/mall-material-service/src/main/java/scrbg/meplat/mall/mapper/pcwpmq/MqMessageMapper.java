//package scrbg.meplat.mall.mapper.pcwpmq;
//
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import org.apache.ibatis.annotations.*;
//import org.springframework.stereotype.Repository;
//import scrbg.meplat.mall.dto.pcwpmq.MqMessageDTO;
//
//import java.util.List;
//
//@Mapper
//@Repository
//public interface MqMessageMapper  extends BaseMapper<MqMessageDTO> {
//    // 1. 内置方法已包含
//
//    // 2. 批量更新消费状态
//    @Update("<script>" +
//            "UPDATE mq_message SET is_get = '1' WHERE id IN " +
//            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
//            "#{id}" +
//            "</foreach>" +
//            "</script>")
//    int batchMarkAsConsumed(@Param("ids") List<Integer> ids);
//
//    // 3. 按类型查询未消费消息
//    @Select("SELECT id,type,time,is_get FROM mq_message WHERE type = #{type} AND is_get = '0'")
//    List<MqMessageDTO> selectUnconsumedByType(String type);
//
//    @Select("SELECT id,json_text,type,time,is_get FROM mq_message WHERE id = #{id}")
//    List<MqMessageDTO> selectUnconsumedById(int id);
//
//    @Options(useGeneratedKeys = true, keyProperty = "id")
//    @Insert("INSERT INTO mq_message(jsontext, type, time, is_get) " +
//            "VALUES (#{jsonText}, #{type}, #{time}, #{isGet})")
//    int insertWithId(MqMessageDTO entity);
//}
