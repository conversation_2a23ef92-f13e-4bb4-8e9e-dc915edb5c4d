package scrbg.meplat.mall.util.poi.exp.processor;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.expression.EvaluationException;
import org.springframework.expression.Expression;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.util.CollectionUtils;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.util.poi.common.Constants;
import scrbg.meplat.mall.util.poi.exception.PoiElErrorCode;
import scrbg.meplat.mall.util.poi.exp.context.PoiExporterContext;
import scrbg.meplat.mall.util.poi.exp.domain.MutiRowModel;
import scrbg.meplat.mall.util.poi.log.Log;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <poi:foreach></poi:foreach>的处理器
 *
 * <AUTHOR>
 */
public class ForeachRowProcessor implements RowProcessor {
    private static final Logger logger = LoggerFactory.getLogger(ForeachRowProcessor.class);

    @Override
    public int dealRow(XSSFRow currentRow, PoiExporterContext peContext) {
        XSSFCell beginCell = currentRow.getCell(support(currentRow));

        // 从beginCell中找出list key
        String beginCellContent = beginCell.getStringCellValue();
        Matcher ma = Pattern.compile(Constants.POI_FOREACH_START_REGEXP).matcher(beginCellContent);
        String key = null;
        if (ma.find()) {
            key = ma.group(1).trim();
        } else {
            throw PoiElErrorCode.TAG_NOT_FOUND.exp(beginCellContent, Constants.POI_FOREACH_START_REGEXP);
        }

        MutiRowModel tpRow = new MutiRowModel();
        tpRow.setListKey(key);
        tpRow.setBegin(beginCell.getRowIndex());
        Map<Integer, Map<Integer, Object>> cellMap = new TreeMap<>();
        int beginRowNum = beginCell.getRow().getRowNum();
        Pattern prePattern = Pattern.compile(Constants.POI_FOREACH_START_REGEXP);
        Pattern postPattern = Pattern.compile(Constants.POI_FOREACH_END_REGEXP);
        while (beginRowNum <= beginCell.getSheet().getLastRowNum() && null == tpRow.getEnd()) {
            XSSFRow row = beginCell.getSheet().getRow(beginRowNum);
            if (row == null) {
                // throw new Exception();
            }
            Map<Integer, Object> map = new HashMap<>();
            short end = row.getLastCellNum();
            for (int k = 0; k <= end; k++) {
                XSSFCell cell = row.getCell(k);
                if (null == cell) {
                    continue;
                }
                String cellValue = getCellValueAsString(cell).trim();
                if (cellValue.equals("")) {
                    continue;
                }
                String value = cellValue;
                Matcher preMatcher = prePattern.matcher(cellValue);
                if (preMatcher.find()) {
                    value = preMatcher.replaceAll("");
                }
                Matcher postMatcher = postPattern.matcher(cellValue);
                if (postMatcher.find()) {
                    value = postMatcher.replaceAll("");
                    tpRow.setEnd(beginRowNum);
                }
                // 存放<单元格列号, 单元格内容>。单元格内容是除去tag之外的
                map.put(k, value);
            }
            cellMap.put(beginRowNum, map);
            beginRowNum++;
        }
        tpRow.setCellMap(cellMap);

        Object rootObject = peContext.getRootObjectMap().get(key);
        if (!(rootObject instanceof List)) {
            throw PoiElErrorCode.ILLEGAL_PARAM.exp("<poi:foreach>中list：" + key + "对应的值应该为List");
        }


        List<?> ls = (List<?>) rootObject;

//        if(!CollectionUtils.isEmpty(ls)) {
            setMutiData(beginCell, ls, tpRow, peContext);
//        }
        return ls.size() * (tpRow.getEnd() - tpRow.getBegin() + 1);
    }

    /**
     * 进行拷贝和赋值
     *
     * @param cell
     * @param ls
     * @param tpRow
     */
    private static void setMutiData(XSSFCell cell, List<?> ls, MutiRowModel tpRow, PoiExporterContext peContext) {
        XSSFSheet sheet = cell.getSheet();
        int mutiRow = 0;
        try {
            mutiRow = tpRow.getEnd() - tpRow.getBegin() + 1; // 循环的行数
        }catch (Exception e) {
            throw new BusinessException(500,"导出excel出现异常" + e.getMessage());
        }
        // 行往下移
        if (ls.size() > 1) { // fix 只有一条数据时，不需要下移
            sheet.shiftRows(tpRow.getEnd() + 1, sheet.getLastRowNum() + 3, (ls.size() - 1) * mutiRow, true, false);
        }

        Map<Integer, Map<Integer, Object>> cellMap = tpRow.getCellMap();

        int myKey = 0;

        if (ls.size() == 0) {
            for (Integer key1 : cellMap.keySet()) {
                XSSFRow curRow = sheet.getRow(key1);
                Map<Integer, Object> map = cellMap.get(key1);
                for (Integer key : map.keySet()) {
                    String cellContent = map.get(key) == null ? "" : (String) map.get(key);
                    // 去除两边空格
                    if (cellContent != "") {
                        cellContent = cellContent.trim();
                    }
                    XSSFCell c = curRow.getCell(key);
                    c.setCellValue("");
                }
            }
        }

        for (int i = 0; i < ls.size(); i++) {
            // 真实数据
            Object rootObjectItem = ls.get(i);
            for (Integer key1 : cellMap.keySet()) { // key1为row行号
                XSSFRow curRow = null;
                if (i == 0) {
                    curRow = sheet.getRow(key1);
                } else {
                    myKey = key1;
                    int num = i * mutiRow + key1;
                    curRow = sheet.createRow(num);
                    // 拷贝样式
                    copyCellStyle(sheet.getRow(key1), curRow);

                    // ye:不知道为什么高度会没设置，所以这里再设置一次
                    curRow.setHeightInPoints(sheet.getRow(key1).getHeightInPoints()); // 设置行高度

                    // 合并
                    copyMergeRegion(sheet, key1, num);
                }

                // 处理当前行里面的每个单元格：替换内容
                // 每一行
                Map<Integer, Object> map = cellMap.get(key1);
                for (Integer key : map.keySet()) {
                    String cellContent = map.get(key) == null ? "" : (String) map.get(key);
                    // 去除两边空格
                    if (cellContent != "") {
                        cellContent = cellContent.trim();
                    }
                    XSSFCell c = curRow.getCell(key);
                    // index自动+1
                    if ("#{index}".equals(cellContent)) {
                        c.setCellValue(i + 1);
                    } else {
                        XSSFCellStyle cellStyle = c.getSheet().getWorkbook().createCellStyle();
                        cellStyle.cloneStyleFrom(c.getCellStyle());
                        cellStyle.setWrapText(true);
                        c.setCellStyle(cellStyle);
                        String str = parseValue(cellContent, rootObjectItem, peContext);
                        if(StringUtils.isNotBlank(str)) {
                            if (str.matches("-?\\d+(\\.\\d+)?")) {
                                if(str.length() >=12) {
                                    c.setCellValue(str);
                                }else {
                                    Double aDouble = Double.valueOf(str);
                                    c.setCellValue(aDouble);
                                }
                            } else {
                                c.setCellValue(str);
                            }
                        }else {
                            c.setCellValue(str);
                        }
                    }
                }
            }
        }


        /**
         * 处理重复首行名称合并
         */
        ArrayList<String> strList = new ArrayList<>();
        for (int i = 0; i < ls.size(); i++) {
            // 循环行
            int num = i * mutiRow + myKey;
            XSSFRow row = sheet.getRow(num);
            // 拿到每一行的内容
            XSSFCell cell1 = row.getCell(0);
			String v = null;
            if (cell1.getCellType() == CellType.STRING) {
				v = cell1.getStringCellValue();
            }
            if (cell1.getCellType() == CellType.NUMERIC) {
				v = cell1.getNumericCellValue() + "";
            }
            if (num != 0) {
                for (int i1 = 0; i1 < num; i1++) {
                    strList.add("@@占位");
                }
            }
            strList.add(num, v);
            if (strList.size() != 1) {
                // 拿到上一次的数据
				String upV = null;
				XSSFCell cell2 = sheet.getRow(num - 1).getCell(0);
				if (cell2.getCellType() == CellType.STRING) {
					upV = cell2.getStringCellValue();
				}
				if (cell2.getCellType() == CellType.NUMERIC) {
					upV = cell2.getNumericCellValue() + "";
				}
                // 如果和上次相等
                if (v.equals(upV)) {
                    // 如果是最后一次循环
                    if (i == ls.size() - 1) {
                        // 会出现最后一样问题！
                        // 合并
                        CellRangeAddress newRegion = new CellRangeAddress(strList.indexOf(upV), num, 0, 0);
                        sheet.addMergedRegion(newRegion);
                    }
                } else {
                    // 如果和上一次不相等，判断是否上一次是否有多个，如果有触发合并
                    if (strList.indexOf(upV) != strList.lastIndexOf(upV)) {
                        // 合并
                        CellRangeAddress newRegion = new CellRangeAddress(strList.indexOf(upV), strList.lastIndexOf(upV), 0, 0);
                        sheet.addMergedRegion(newRegion);
                    }
                }
            }
        }

    }

    /**
     * 安全地获取单元格值作为字符串
     */
    private static String getCellValueAsString(XSSFCell cell) {
        if (cell == null) {
            return "";
        }

        try {
            switch (cell.getCellType()) {
                case STRING:
                    try {
                        return cell.getStringCellValue();
                    } catch (Exception e) {
                        return cell.getRichStringCellValue().getString();
                    }
                case NUMERIC:
                    if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                        return cell.getDateCellValue().toString();
                    } else {
                        double numericValue = cell.getNumericCellValue();
                        // 如果是整数，不显示小数点
                        if (numericValue == (long) numericValue) {
                            return String.valueOf((long) numericValue);
                        } else {
                            return String.valueOf(numericValue);
                        }
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    try {
                        // 先尝试获取计算后的字符串值
                        return cell.getStringCellValue();
                    } catch (Exception e1) {
                        try {
                            // 如果失败，尝试获取数值
                            return String.valueOf(cell.getNumericCellValue());
                        } catch (Exception e2) {
                            try {
                                // 最后尝试获取公式本身
                                return cell.getCellFormula();
                            } catch (Exception e3) {
                                return "";
                            }
                        }
                    }
                case BLANK:
                case _NONE:
                default:
                    return "";
            }
        } catch (Exception e) {
            // 如果所有方法都失败，返回空字符串
            return "";
        }
    }

    private static String parseValue(String cellContent, Object rootObjectItem, PoiExporterContext peContext) {
        // 处理EL表达式
        Expression expression = peContext.getSpelExpParser().parseExpression(cellContent, new TemplateParserContext());
        String parsedContent = null;
        try {
            parsedContent = expression.getValue(PoiExporterContext.EVAL_CONTEXT, rootObjectItem, String.class);
        } catch (EvaluationException e) {
            logger.error(Log.op("ForeachRowProcessor#parseValue").msg("EL解析出错啦").toString(), e);
            return cellContent; // 异常后，原样返回，不再处理
        }
        // 处理${key}
        return parsedContent == null ? "" : DefaultRowProcessor.resolve(parsedContent, peContext);
    }

    /**
     * 拷贝样式
     *
     * @param src
     * @param des
     */
    private static void copyCellStyle(XSSFRow src, XSSFRow des) {
        for (int i = src.getFirstCellNum(); i < src.getLastCellNum(); i++) {
            des.createCell(i).setCellStyle(src.getCell(i).getCellStyle());
        }
    }

    /**
     * 拷贝合并单元格
     *
     * @param sheet
     * @param srcRow
     * @param desRow
     */
    private static void copyMergeRegion(XSSFSheet sheet, int srcRow, int desRow) {
        for (int j = 0; j < sheet.getNumMergedRegions(); j++) {
            CellRangeAddress oldRegion = sheet.getMergedRegion(j);
            if ((oldRegion.getFirstRow() == srcRow) && (oldRegion.getLastRow() == srcRow)) {
                int oldFirstCol = oldRegion.getFirstColumn();
                int oldLastCol = oldRegion.getLastColumn();
                CellRangeAddress newRegion = new CellRangeAddress(desRow, desRow, oldFirstCol, oldLastCol);
//				 System.out.println(desRow+","+desRow+","+oldFirstCol+","+oldLastCol);
                sheet.addMergedRegion(newRegion);
            }
        }
    }

    @Override
    public int support(XSSFRow row) {
        for (int k = 0; k <= row.getLastCellNum(); k++) {
            XSSFCell cell = row.getCell(k);

            // 不判断类型
            if (null != cell) {
                if (cell.getCellType() == CellType.STRING) {
                    String content = cell.getStringCellValue().trim();
                    Pattern prePattern = Pattern.compile(Constants.POI_FOREACH_START_REGEXP);
                    Matcher matcher = prePattern.matcher(content);
                    if (matcher.find()) {
                        return k;
                    }
                }
                if (cell.getCellType() == CellType.NUMERIC) {
                    double content = cell.getNumericCellValue();
                    String c = content + "";
                    Pattern prePattern = Pattern.compile(Constants.POI_FOREACH_START_REGEXP);
                    Matcher matcher = prePattern.matcher(c);
                    if (matcher.find()) {
                        return k;
                    }
                }

            }
        }
        return -1;
    }

    public static void main(String[] args) {
        String content = "<poi:foreach list=\"list\"> #{index}";
//		String content = "<poi:foreach list=\"list\">";
        boolean flag = content.matches("<poi:foreach\\s+list=\"(\\w+)\">");
//		PoiGenericSheetVo<OrderImportVo> genericSheetVo = PoiImporter.importFirstSheetFrom(is, OrderImportVo.class);
        Pattern prePattern = Pattern.compile(Constants.POI_FOREACH_START_REGEXP);
        Matcher matcher = prePattern.matcher(content);
        boolean f = matcher.find();
        System.out.println(f);
        System.out.println(matcher.replaceAll(""));
    }

}
