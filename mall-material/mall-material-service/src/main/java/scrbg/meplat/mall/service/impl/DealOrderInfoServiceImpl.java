package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.seata.common.util.CollectionUtils;
import io.seata.common.util.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.DealOrderInfoMapper;
import scrbg.meplat.mall.service.*;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.util.TaxCalculator;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
import scrbg.meplat.mall.vo.w.BatchDealOrderInfoDTO;
import scrbg.meplat.mall.vo.w.DealOrderInfoDTO;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @描述：结算订单明细 服务类
 * @作者: ye
 * @日期: 2023-06-16
 */
@Service
public class DealOrderInfoServiceImpl extends ServiceImpl<DealOrderInfoMapper, DealOrderInfo> implements DealOrderInfoService {
    @Autowired
    private MallConfig mallConfig;

    @Autowired
    private ProductService productService;

    @Autowired
    private InterfaceLogsService interfaceLogsService;

    @Autowired
    private ShopService shopService;
    @Autowired
    private EnterpriseInfoService enterpriseInfoService;

    @Autowired
    private MaterialReconciliationService materialReconciliationService;


    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<DealOrderInfo> queryWrapper) {
        IPage<DealOrderInfo> page = this.page(
                new Query<DealOrderInfo>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(DealOrderInfo dealOrderInfo) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(dealOrderInfo);
    }

    @Override
    public void update(DealOrderInfo dealOrderInfo) {
        super.updateById(dealOrderInfo);
    }


    @Override
    public DealOrderInfo getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    /**
     * 店铺查询结算信息
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils shopListByEntity(JSONObject jsonObject, LambdaQueryWrapper<DealOrderInfo> q) {

//        String orderSn = (String) jsonObject.get("orderSn");
        String productName = (String) jsonObject.get("productName");
        String keywords = (String) jsonObject.get("keywords");
        String bugOrgName = (String) jsonObject.get("bugOrgName");

        String startFinishDate = (String) jsonObject.get("startFinishDate");
        String endFinishDate = (String) jsonObject.get("endFinishDate");
        String shopId = (String) jsonObject.get("shopId");

        String belowPrice = (String) jsonObject.get("belowPrice");
        String abovePrice = (String) jsonObject.get("abovePrice");

        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(DealOrderInfo::getProductName, keywords)
                        .or()
                        .like(DealOrderInfo::getBugOrgName, keywords);

            });
        }
//        q.eq(StringUtils.isNotBlank(orderSn), DealOrderInfo::getOrderSn, orderSn);
        q.eq(StringUtils.isNotBlank(productName), DealOrderInfo::getProductName, productName);
        q.like(StringUtils.isNotBlank(bugOrgName), DealOrderInfo::getBugOrgName, bugOrgName);

        q.ge(StringUtils.isNotBlank(abovePrice), DealOrderInfo::getAmount, abovePrice);
        q.le(StringUtils.isNotBlank(belowPrice), DealOrderInfo::getAmount, belowPrice);
        q.orderByDesc(DealOrderInfo::getFinishDate);
        q.between(StringUtils.isNotEmpty(startFinishDate) && StringUtils.isNotEmpty(endFinishDate), DealOrderInfo::getFinishDate, startFinishDate, endFinishDate);
        // 只查询本店铺的数据
        if (shopId != null && shopId != "") {
            q.eq(DealOrderInfo::getShopId, shopId);
        } else {
            q.eq(DealOrderInfo::getShopId, ThreadLocalUtil.getCurrentUser().getShopId());
        }
        IPage<DealOrderInfo> page = this.page(
                new Query<DealOrderInfo>().getPage(jsonObject),
                q
        );
        ArrayList<DealOrderInfo> dealOrderInfos = new ArrayList<>();
        List<DealOrderInfo> records = page.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<DealOrderInfo> list = list(q);
            BigDecimal bigDecimal = new BigDecimal(0);
            for (DealOrderInfo dealOrderInfo : list) {
                bigDecimal = bigDecimal.add(dealOrderInfo.getAmount());
            }
//            BigDecimal bigDecimal = new BigDecimal(0);
//            for (DealOrderInfo record : records) {
//                bigDecimal=bigDecimal.add(record.getAmount());
//            }
            records.get(0).setCountAmount(bigDecimal);
        }
        Map<String, List<DealOrderInfo>> groupedByCompany = records.stream().collect(Collectors.groupingBy(DealOrderInfo::getBuyOrgId));
        groupedByCompany.forEach((company, list) -> {
            List<DealOrderInfo> sortedList = list.stream()
                    .sorted(Comparator.comparing(DealOrderInfo::getFinishDate))
                    .collect(Collectors.toList());

            DealOrderInfo dealOrderInfo = sortedList.get(0);
            QueryWrapper<DealOrderInfo> wrapper = new QueryWrapper<>();
            wrapper.eq("buy_org_id", dealOrderInfo.getBuyOrgId());
            wrapper.lt("finish_date", dealOrderInfo.getFinishDate());
            wrapper.select("sum(amount) as accumulateAmount");
            DealOrderInfo date = baseMapper.selectOne(wrapper);
            BigDecimal accumulateAmount = new BigDecimal(0);
            if (date == null) {
                accumulateAmount = new BigDecimal(0);
            } else {
                accumulateAmount = date.getAccumulateAmount();
            }

            for (DealOrderInfo obj : sortedList) {
                accumulateAmount = accumulateAmount.add(obj.getAmount());
                obj.setAccumulateAmount(accumulateAmount);
                dealOrderInfos.add(obj);
            }
        });
        List<DealOrderInfo> collect = dealOrderInfos.stream()
                .sorted(Comparator.comparing(DealOrderInfo::getFinishDate).reversed())
                .collect(Collectors.toList());
        page.setRecords(collect);
        return new PageUtils(page);
    }


    /**
     * 导出数据
     *
     * @param jsonObject
     * @param q
     * @param response
     */
    @Override
    public void outputExcel(JSONObject jsonObject, QueryWrapper<DealOrderInfo> q, HttpServletResponse response) {
//        String orderSn = (String) jsonObject.get("orderSn");
        String productName = (String) jsonObject.get("productName");
        String buyOrgName = (String) jsonObject.get("buyOrgName");
        String keywords = (String) jsonObject.get("keywords");

        String startFinishDate = (String) jsonObject.get("startFinishDate");
        String endFinishDate = (String) jsonObject.get("endFinishDate");
        String shopId = (String) jsonObject.get("shopId");

        String belowPrice = (String) jsonObject.get("belowPrice");
        String abovePrice = (String) jsonObject.get("abovePrice");
        List<String> ids = (ArrayList<String>) jsonObject.get("ids");

        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like("product_name", keywords)
                        .or()
                        .like("buy_org_name", keywords);

            });
        }
//        q.eq(StringUtils.isNotBlank(orderSn), "order_sn", orderSn);
        q.eq(StringUtils.isNotBlank(productName), "product_name", productName);
        q.like(StringUtils.isNotBlank(buyOrgName), "bug_org_name", buyOrgName);

        q.in(CollectionUtils.isNotEmpty(ids), "deal_order_info_id", ids);
        q.ge(StringUtils.isNotBlank(abovePrice), "amount", abovePrice);
        q.le(StringUtils.isNotBlank(belowPrice), "amount", belowPrice);

        q.between(StringUtils.isNotEmpty(startFinishDate) && StringUtils.isNotEmpty(endFinishDate), "finish_date", startFinishDate, endFinishDate);
        // 只查询本店铺的数据
        if (shopId != null && shopId != "") {
            q.eq("shop_id", shopId);
        } else {
            q.eq("shop_id", ThreadLocalUtil.getCurrentUser().getShopId());
        }


        q.select(
                "bug_org_name",
                "reconciliation_amount",
                "buy_org_id",
                "product_name",
                "number",
                "amount",
                "finish_date",
                "DATE_FORMAT(finish_date, '%Y-%m-%d %H:%i:%s') AS finishDateStr"
        );

        // 只导出4000条数据
        q.last("LIMIT " + 4000);
        q.orderByDesc("finish_date");
        QueryWrapper<DealOrderInfo> clone = q.clone();
        List<DealOrderInfo> dealOrderInfos = baseMapper.selectList(q);

        ArrayList<DealOrderInfo> dataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dealOrderInfos)) {
            clone.select("sum(amount) as countAmount");
            DealOrderInfo dealOrderInfo = baseMapper.selectOne(clone);
            dealOrderInfos.get(0).setCountAmount(dealOrderInfo.getCountAmount());
        }
        if (dealOrderInfos != null && dealOrderInfos.size() > 0) {
            Map<String, List<DealOrderInfo>> groupedByCompany = dealOrderInfos.stream()
                    .collect(Collectors.groupingBy(DealOrderInfo::getBuyOrgId));
            groupedByCompany.forEach((company, list) -> {
                List<DealOrderInfo> sortedList = list.stream()
                        .sorted(Comparator.comparing(DealOrderInfo::getFinishDate))
                        .collect(Collectors.toList());

                DealOrderInfo dealOrderInfo = sortedList.get(0);
                QueryWrapper<DealOrderInfo> wrapper = new QueryWrapper<>();
                wrapper.eq("buy_org_id", dealOrderInfo.getBuyOrgId());
                wrapper.lt("finish_date", dealOrderInfo.getFinishDate());
                wrapper.select("sum(amount) as accumulateAmount");
                DealOrderInfo date = baseMapper.selectOne(wrapper);
                if (date == null) {
                    date = new DealOrderInfo();
                    date.setAccumulateAmount(BigDecimal.valueOf(0));
                }
                BigDecimal accumulateAmount = new BigDecimal(0);
                for (int i = 0; i < sortedList.size(); i++) {
                    if (i == 0) {
                        accumulateAmount = date.getAccumulateAmount().add(sortedList.get(i).getAmount());
                    } else {
                        accumulateAmount = accumulateAmount.add(sortedList.get(i).getAmount());
                    }
                    sortedList.get(i).setAccumulateAmount(accumulateAmount);
                    dataList.add(sortedList.get(i));
                }
            });
        }
        dataList.sort((t1, t2) -> t2.getFinishDate().compareTo(t1.getFinishDate()));

        String src = mallConfig.templateFormUrl;
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", dataList);
        dataMap.put("countAmountTotal", dealOrderInfos.get(0).getCountAmount());
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "供应商结算模板.xlsx", src, "供应商结算.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }
    }

    /**
     * 平台查询结算信息
     *
     * @param jsonObject
     * @param q2
     * @return
     */
    @Override
    public PageUtils platformListByEntity(JSONObject jsonObject, LambdaQueryWrapper<DealOrderInfo> q2) {
        QueryWrapper<DealOrderInfo> q = new QueryWrapper<>();
//        String orderSn = (String) jsonObject.get("orderSn");
        String productName = (String) jsonObject.get("productName");
        String keywords = (String) jsonObject.get("keywords");
        String supplierName = (String) jsonObject.get("supplierName");
        String shopName = (String) jsonObject.get("shopName");

        String startFinishDate = (String) jsonObject.get("startFinishDate");
        String endFinishDate = (String) jsonObject.get("endFinishDate");

        String belowPrice = (String) jsonObject.get("belowPrice");
        String abovePrice = (String) jsonObject.get("abovePrice");

        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like("product_name", keywords)
                        .or()
//                        .like("order_sn", keywords)
//                        .or()
                        .like("supplier_name", keywords)
                        .or()
                        .like("shop_name", keywords);
            });
        }
//        q.eq(StringUtils.isNotBlank(orderSn), DealOrderInfo::getOrderSn, orderSn);
        q.eq(StringUtils.isNotBlank(supplierName), "supplier_name", supplierName);
        q.eq(StringUtils.isNotBlank(shopName), "shop_name", shopName);
        q.eq(StringUtils.isNotBlank(productName), "product_name", productName);

        q.ge(StringUtils.isNotBlank(abovePrice), "amount", abovePrice);
        q.le(StringUtils.isNotBlank(belowPrice), "amount", belowPrice);

        q.orderByDesc("finish_date");
        q.between(StringUtils.isNotEmpty(startFinishDate) && StringUtils.isNotEmpty(endFinishDate), "gmt_create", startFinishDate, endFinishDate);

        QueryWrapper<DealOrderInfo> clone = new QueryWrapper<>();


        IPage<DealOrderInfo> page = this.page(
                new Query<DealOrderInfo>().getPage(jsonObject),
                q
        );
        ArrayList<DealOrderInfo> dealOrderInfos = new ArrayList<>();
        List<DealOrderInfo> records = page.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            clone.between(StringUtils.isNotEmpty(startFinishDate) && StringUtils.isNotEmpty(endFinishDate), "gmt_create", startFinishDate, endFinishDate);
            clone.select("sum(amount) as countAmount");
            DealOrderInfo dealOrderInfo = baseMapper.selectOne(clone);
            records.get(0).setCountAmount(dealOrderInfo.getCountAmount());
        }

        Map<String, List<DealOrderInfo>> groupedByCompany = records.stream().collect(Collectors.groupingBy(DealOrderInfo::getSupplierId));
        groupedByCompany.forEach((company, list) -> {
            List<DealOrderInfo> sortedList = list.stream()
                    .sorted(Comparator.comparing(DealOrderInfo::getFinishDate))
                    .collect(Collectors.toList());

            DealOrderInfo dealOrderInfo = sortedList.get(0);
            QueryWrapper<DealOrderInfo> wrapper = new QueryWrapper<>();
            wrapper.eq("supplier_id", dealOrderInfo.getSupplierId());
            wrapper.lt("finish_date", dealOrderInfo.getFinishDate());
            wrapper.select("sum(amount) as accumulateAmount");
            DealOrderInfo date = baseMapper.selectOne(wrapper);
            BigDecimal accumulateAmount = new BigDecimal(0);
            if (date == null) {
                accumulateAmount = new BigDecimal(0);
            } else {
                accumulateAmount = date.getAccumulateAmount();
            }

            for (DealOrderInfo obj : sortedList) {
                accumulateAmount = accumulateAmount.add(obj.getAmount());
                obj.setAccumulateAmount(accumulateAmount);
                dealOrderInfos.add(obj);
            }
        });

        List<DealOrderInfo> collect = dealOrderInfos.stream()
                .sorted(Comparator.comparing(DealOrderInfo::getFinishDate).reversed())
                .collect(Collectors.toList());
        page.setRecords(collect);
        return new PageUtils(page);
    }


    /**
     * 导出数据平台
     *
     * @param jsonObject
     * @param q
     * @param response
     */
    @Override
    public void platformOutputExcel(JSONObject jsonObject, QueryWrapper<DealOrderInfo> q, HttpServletResponse response) {
//        String orderSn = (String) jsonObject.get("orderSn");
        String productName = (String) jsonObject.get("productName");
        String supplierName = (String) jsonObject.get("supplierName");
        String shopName = (String) jsonObject.get("shopName");
        String keywords = (String) jsonObject.get("keywords");

        String startFinishDate = (String) jsonObject.get("startFinishDate");
        String endFinishDate = (String) jsonObject.get("endFinishDate");

        String belowPrice = (String) jsonObject.get("belowPrice");
        String abovePrice = (String) jsonObject.get("abovePrice");
        List<String> ids = (ArrayList<String>) jsonObject.get("ids");

        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like("product_name", keywords)
                        .or()
//                        .like("order_sn", keywords)
//                        .or()
                        .like("supplier_name", keywords)
                        .or()
                        .like("shop_name", keywords);
            });
        }
//        q.eq(StringUtils.isNotBlank(orderSn), "order_sn", orderSn);
        q.eq(StringUtils.isNotBlank(supplierName), "supplier_name", supplierName);
        q.eq(StringUtils.isNotBlank(shopName), "shop_name", shopName);
        q.eq(StringUtils.isNotBlank(productName), "product_name", productName);

        q.in(CollectionUtils.isNotEmpty(ids), "deal_order_info_id", ids);
        q.ge(StringUtils.isNotBlank(abovePrice), "amount", abovePrice);
        q.le(StringUtils.isNotBlank(belowPrice), "amount", belowPrice);

        q.between(StringUtils.isNotEmpty(startFinishDate) && StringUtils.isNotEmpty(endFinishDate), "finish_date", startFinishDate, endFinishDate);

        q.orderByDesc("finish_date");

        // 只导出4000条数据
        q.last("LIMIT " + 4000);
        QueryWrapper<DealOrderInfo> clone = q.clone();
        q.select(
                "shop_name",
                "supplier_name",
//                "order_sn",
                "product_name",
                "number",
                "amount",
                "DATE_FORMAT(finish_date, '%Y-%m-%d %H:%i:%s') AS finishDateStr"
        );


        List<DealOrderInfo> dealOrderInfos = baseMapper.selectList(q);
        if (CollectionUtils.isNotEmpty(dealOrderInfos)) {
            clone.select("sum(amount) as countAmount");
            DealOrderInfo dealOrderInfo = baseMapper.selectOne(clone);
            dealOrderInfos.get(0).setCountAmount(dealOrderInfo.getCountAmount());
        }

        String src = mallConfig.templateFormUrl;
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", dealOrderInfos);
        dataMap.put("countAmountTotal", dealOrderInfos.get(0).getCountAmount());
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "平台物资结算统计模板.xlsx", src, "物资结算统计.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }
    }


    /**
     * 供应商平台查询结算信息
     *
     * @param jsonObject
     * @param q2
     * @return
     */
    @Override
    public PageUtils vendorListByEntity(JSONObject jsonObject, LambdaQueryWrapper<DealOrderInfo> q2) {
        QueryWrapper<DealOrderInfo> q = new QueryWrapper<>();
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        String enterpriseId = currentUser.getEnterpriseId();
        q.eq(StringUtils.isNotBlank(enterpriseId), "supplier_id", enterpriseId);

//        String orderSn = (String) jsonObject.get("orderSn");
        String productName = (String) jsonObject.get("productName");
        String keywords = (String) jsonObject.get("keywords");
        String supplierName = (String) jsonObject.get("supplierName");
        String shopName = (String) jsonObject.get("shopName");

        String startFinishDate = (String) jsonObject.get("startFinishDate");
        String endFinishDate = (String) jsonObject.get("endFinishDate");

        String belowPrice = (String) jsonObject.get("belowPrice");
        String abovePrice = (String) jsonObject.get("abovePrice");

//        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();

        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like("product_name", keywords)
                        .or()
//                        .like("order_sn", keywords)
//                        .or()
                        .like("supplier_name", keywords)
                        .or()
                        .like("shop_name", keywords);
            });
        }
//      q.eq(StringUtils.isNotBlank(orderSn), DealOrderInfo::getOrderSn, orderSn);
        q.eq(StringUtils.isNotBlank(supplierName), "supplier_name", supplierName);
        q.eq(StringUtils.isNotBlank(shopName), "shop_name", shopName);

        q.eq(StringUtils.isNotBlank(productName), "product_name", productName);

        q.ge(StringUtils.isNotBlank(abovePrice), "amount", abovePrice);
        q.le(StringUtils.isNotBlank(belowPrice), "amount", belowPrice);

        q.orderByDesc("finish_date");
        q.between(StringUtils.isNotEmpty(startFinishDate) && StringUtils.isNotEmpty(endFinishDate), "finish_date", startFinishDate, endFinishDate);

        QueryWrapper<DealOrderInfo> clone = q.clone();

        IPage<DealOrderInfo> page = this.page(
                new Query<DealOrderInfo>().getPage(jsonObject),
                q
        );

        List<DealOrderInfo> records = page.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            clone.select("sum(amount) as countAmount");
            DealOrderInfo dealOrderInfo = baseMapper.selectOne(clone);
            records.get(0).setCountAmount(dealOrderInfo.getCountAmount());

        }
        return new PageUtils(page);
    }

    /**
     * 导出供应数据平台
     *
     * @param jsonObject
     * @param q
     * @param response
     */
    @Override
    public void supplyPlatformOutputExcel(JSONObject jsonObject, QueryWrapper<DealOrderInfo> q, HttpServletResponse response) {
//        String orderSn = (String) jsonObject.get("orderSn");

        String productName = (String) jsonObject.get("productName");
        String supplierName = (String) jsonObject.get("supplierName");
        String shopName = (String) jsonObject.get("shopName");
        String keywords = (String) jsonObject.get("keywords");

        String startFinishDate = (String) jsonObject.get("startFinishDate");
        String endFinishDate = (String) jsonObject.get("endFinishDate");

        String belowPrice = (String) jsonObject.get("belowPrice");
        String abovePrice = (String) jsonObject.get("abovePrice");
        List<String> ids = (ArrayList<String>) jsonObject.get("ids");

        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like("product_name", keywords)
                        .or()
//                        .like("order_sn", keywords)
//                        .or()
                        .like("supplier_name", keywords)
                        .or()
                        .like("shop_name", keywords);
            });
        }
//        q.eq(StringUtils.isNotBlank(orderSn), "order_sn", orderSn);
        q.eq(StringUtils.isNotBlank(supplierName), "supplier_name", supplierName);
        q.eq(StringUtils.isNotBlank(shopName), "shop_name", shopName);
        q.eq(StringUtils.isNotBlank(productName), "product_name", productName);

        q.in(CollectionUtils.isNotEmpty(ids), "deal_order_info_id", ids);
        q.ge(StringUtils.isNotBlank(abovePrice), "amount", abovePrice);
        q.le(StringUtils.isNotBlank(belowPrice), "amount", belowPrice);

        q.between(StringUtils.isNotEmpty(startFinishDate) && StringUtils.isNotEmpty(endFinishDate), "finish_date", startFinishDate, endFinishDate);

        q.orderByDesc("finish_date");

        // 只导出4000条数据
        q.last("LIMIT " + 4000);
        QueryWrapper<DealOrderInfo> clone = q.clone();
        q.select(
                "shop_name",
//                "order_sn",
                "product_name",
                "number",
                "amount",
                "DATE_FORMAT(finish_date, '%Y-%m-%d %H:%i:%s') AS finishDateStr"
        );


        List<DealOrderInfo> dealOrderInfos = baseMapper.selectList(q);
        ArrayList<DealOrderInfo> dataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dealOrderInfos)) {
            clone.select("sum(amount) as countAmount");
            DealOrderInfo dealOrderInfo = baseMapper.selectOne(clone);
            dealOrderInfos.get(0).setCountAmount(dealOrderInfo.getCountAmount());
        }
        if (dealOrderInfos != null && dealOrderInfos.size() > 0) {
            Map<String, List<DealOrderInfo>> groupedByCompany = dealOrderInfos.stream()
                    .collect(Collectors.groupingBy(DealOrderInfo::getSupplierId));
            groupedByCompany.forEach((company, list) -> {
                List<DealOrderInfo> sortedList = list.stream()
                        .sorted(Comparator.comparing(DealOrderInfo::getFinishDate))
                        .collect(Collectors.toList());

                DealOrderInfo dealOrderInfo = sortedList.get(0);
                QueryWrapper<DealOrderInfo> wrapper = new QueryWrapper<>();
                wrapper.eq("supplier_id", dealOrderInfo.getSupplierId());
                wrapper.lt("finish_date", dealOrderInfo.getFinishDate());
                wrapper.select("sum(amount) as accumulateAmount");
                DealOrderInfo date = baseMapper.selectOne(wrapper);
                if (date == null) {
                    date = new DealOrderInfo();
                    date.setAccumulateAmount(BigDecimal.valueOf(0));
                }
                BigDecimal accumulateAmount = new BigDecimal(0);
                for (int i = 0; i < sortedList.size(); i++) {
                    if (i == 0) {
                        accumulateAmount = date.getAccumulateAmount().add(sortedList.get(i).getAmount());
                    } else {
                        accumulateAmount = accumulateAmount.add(sortedList.get(i).getAmount());
                    }
                    sortedList.get(i).setAccumulateAmount(accumulateAmount);
                    dataList.add(sortedList.get(i));
                }
            });
        }
        dataList.sort((t1, t2) -> t2.getFinishDate().compareTo(t1.getFinishDate()));

        String src = mallConfig.templateFormUrl;
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", dealOrderInfos);
        dataMap.put("countAmountTotal", dealOrderInfos.get(0).getCountAmount());
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "供应商物资结算统计模板.xlsx", src, "供应商物资结算统计.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }
    }


    /**
     * 外部新增
     *
     * @param dto
     * @param keyId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchOutAdd(DealOrderInfoDTO dto, String keyId) {
        // 成功保存日志
        InterfaceLogs iLog = new InterfaceLogs();
        iLog.setFarArguments(JSON.toJSONString(dto));

        List<BatchDealOrderInfoDTO> dtos = dto.getDtos();
        for (BatchDealOrderInfoDTO batchDealOrderInfoDTO : dtos) {
//            if(org.apache.commons.lang.StringUtils.isBlank(batchDealOrderInfoDTO.getReconciliationId())) {
//                throw new BusinessException("未携带对账单id");
//            }
            DealOrderInfo e = new DealOrderInfo();
            BeanUtils.copyProperties(batchDealOrderInfoDTO, e);
            e.setFinishDate(batchDealOrderInfoDTO.getFinishDate());
            // 如果为空通过供应商名称查询   因为公司名称有重复，特意添加远程机构id查询
            String supplierName = e.getSupplierName();
            EnterpriseInfo enterpriseInfo = null;
            try {
                enterpriseInfo = enterpriseInfoService.lambdaQuery()
                        .eq(EnterpriseInfo::getEnterpriseName, supplierName)
                        .select(EnterpriseInfo::getEnterpriseId).one();
            } catch (Exception ex) {
                if (StringUtils.isBlank(e.getSupplierId())) {
                    enterpriseInfo = enterpriseInfoService.lambdaQuery()
                            .eq(EnterpriseInfo::getEnterpriseName, supplierName).and(((t) ->
                                    t.like(EnterpriseInfo::getInteriorId, e.getSupplierId())
                                            .or()
                                            .like(EnterpriseInfo::getEnterpriseId, e.getSupplierId())))
                            .select(EnterpriseInfo::getEnterpriseId).one();
                }
            }

            if (enterpriseInfo != null) {
                String enterpriseId = enterpriseInfo.getEnterpriseId();
                Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, enterpriseId)
                        .select(Shop::getShopName, Shop::getShopId).one();
                if (shop != null) {
                    e.setShopId(shop.getShopId());
                    e.setShopName(shop.getShopName());
                }
            }
            if (e.getReconciliationId() != null) {
                MaterialReconciliation byId = materialReconciliationService.getById(e.getReconciliationId());
                e.setReconciliationAmount(byId.getReconciliationAmount());
            }
            save(e);
        }


        iLog.setSecretKey(keyId);
        iLog.setClassPackage(MaterialReconciliationServiceImpl.class.getName());
        iLog.setMethodName("dealOrderInfoSaveBatch");
        iLog.setLocalArguments(JSON.toJSONString(dto));
        iLog.setIsSuccess(1);
        iLog.setLogType(3);
        interfaceLogsService.create(iLog);
        LogUtil.writeInfoLog(keyId, "dealOrderInfoSaveBatch", null, dtos, null, MaterialReconciliationServiceImpl.class);
    }

    @Override
    public void platformOutputExcel2(JSONObject jsonObject, QueryWrapper<DealOrderInfo> q, HttpServletResponse response) {
        String productName = (String) jsonObject.get("productName");
        String supplierName = (String) jsonObject.get("supplierName");
        String startFinishDate = (String) jsonObject.get("startFinishDate");
        String endFinishDate = (String) jsonObject.get("endFinishDate");

        String belowPrice = (String) jsonObject.get("belowPrice");
        String abovePrice = (String) jsonObject.get("abovePrice");
        List<String> ids = (ArrayList<String>) jsonObject.get("ids");
        String keywords = (String) jsonObject.get("keywords");
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like("product_name", keywords)
                        .or()
//                        .like("order_sn", keywords)
//                        .or()
                        .like("supplier_name", keywords);
            });
        }
        if (ids != null && ids.size() > 0) {
            q.in(CollectionUtils.isNotEmpty(ids), "deal_order_info_id", ids);
        } else {
            q.eq(StringUtils.isNotBlank(supplierName), "supplier_name", supplierName);
            q.ge(StringUtils.isNotBlank(abovePrice), "amount", abovePrice);
            q.le(StringUtils.isNotBlank(belowPrice), "amount", belowPrice);
            q.between(StringUtils.isNotEmpty(startFinishDate) && StringUtils.isNotEmpty(endFinishDate), "gmt_create", startFinishDate, endFinishDate);
            q.orderByDesc("gmt_create");
        }
        q.last("LIMIT " + 4000);
        QueryWrapper<DealOrderInfo> clone = q.clone();
        q.select(
                "supplier_id",
                "supplier_name",
                "bug_org_name",
                "reconciliation_amount",
                "product_name",
                "amount",
                "finish_date",
                "DATE_FORMAT(gmt_create, '%Y-%m-%d %H:%i:%s') AS finishDateStr"
        );
        List<DealOrderInfo> dealOrderInfos = baseMapper.selectList(q);
        ArrayList<DealOrderInfo> dataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dealOrderInfos)) {
            clone.select("sum(amount) as countAmount");
            DealOrderInfo dealOrderInfo = baseMapper.selectOne(clone);
            dealOrderInfos.get(0).setCountAmount(dealOrderInfo.getCountAmount());
        }
        if (dealOrderInfos != null && dealOrderInfos.size() > 0) {
            Map<String, List<DealOrderInfo>> groupedByCompany = dealOrderInfos.stream()
                    .collect(Collectors.groupingBy(DealOrderInfo::getSupplierId));
            groupedByCompany.forEach((company, list) -> {
                List<DealOrderInfo> sortedList = list.stream()
                        .sorted(Comparator.comparing(DealOrderInfo::getFinishDate))
                        .collect(Collectors.toList());

                DealOrderInfo dealOrderInfo = sortedList.get(0);
                QueryWrapper<DealOrderInfo> wrapper = new QueryWrapper<>();
                wrapper.eq("supplier_id", dealOrderInfo.getSupplierId());
                wrapper.lt("gmt_create", dealOrderInfo.getGmtCreate());
                wrapper.select("sum(amount) as accumulateAmount");
                DealOrderInfo date = baseMapper.selectOne(wrapper);
                if (date == null) {
                    date = new DealOrderInfo();
                    date.setAccumulateAmount(BigDecimal.valueOf(0));
                }
                BigDecimal accumulateAmount = new BigDecimal(0);
                for (int i = 0; i < sortedList.size(); i++) {
                    if (i == 0) {
                        accumulateAmount = date.getAccumulateAmount().add(sortedList.get(i).getAmount());
                    } else {
                        accumulateAmount = accumulateAmount.add(sortedList.get(i).getAmount());
                    }
                    sortedList.get(i).setAccumulateAmount(accumulateAmount);
                    dataList.add(sortedList.get(i));
                }
            });
        }
        dataList.sort((t1, t2) -> t2.getFinishDate().compareTo(t1.getFinishDate()));

        String src = mallConfig.templateFormUrl;
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", dataList);
        dataMap.put("countAmountTotal", dealOrderInfos.get(0).getCountAmount());
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "平台物资结算统计模板.xlsx", src, "物资结算统计.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }
    }
}
