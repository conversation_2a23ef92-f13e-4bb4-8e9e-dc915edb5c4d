package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.EnterpriseSituation;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.EnterpriseSituation;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
/**
 * @描述：企业-情况表 服务类
 * @作者: ye
 * @日期: 2025-03-06
 */
public interface EnterpriseSituationService extends IService<EnterpriseSituation> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<EnterpriseSituation> queryWrapper);

        void create(EnterpriseSituation enterpriseSituation);
        void update(EnterpriseSituation enterpriseSituation);
        EnterpriseSituation getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);


    void create1(EnterpriseSituation enterpriseSituation);
}
