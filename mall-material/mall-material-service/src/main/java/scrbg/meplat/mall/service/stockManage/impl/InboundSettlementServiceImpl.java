package scrbg.meplat.mall.service.stockManage.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.InboundSettlementManage;
import scrbg.meplat.mall.mapper.stockManage.InboundSettlementManageMapper;
import scrbg.meplat.mall.service.stockManage.InboundSettlementService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.excel.EasyExcelUtils;
import scrbg.meplat.mall.vo.stockManage.InboundSettlementManageVO;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class InboundSettlementServiceImpl extends ServiceImpl<InboundSettlementManageMapper, InboundSettlementManage> implements InboundSettlementService {


    private InboundSettlementManageMapper inboundSettlementManageMapper;

    private MallConfig mallConfig;

    @Autowired
    public void setInboundSettlementManageMapper(InboundSettlementManageMapper inboundSettlementManageMapper) {
        this.inboundSettlementManageMapper = inboundSettlementManageMapper;
    }
    @Autowired
    public void setMallConfig(MallConfig mallConfig) {
        this.mallConfig = mallConfig;
    }

    @Override
    public void saveSettlement(InboundSettlementManage inboundSettlementManage) {
        inboundSettlementManage.setAuditStatus(0);
        inboundSettlementManage.setReceiveName(ThreadLocalUtil.getCurrentUser().getUserName());
        inboundSettlementManage.setReceivePhone(ThreadLocalUtil.getCurrentUser().getUserMobile());
        inboundSettlementManage.setGmtCreate(new Date());
        save(inboundSettlementManage);
    }

    @Override
    public void saveAndSubmitSettlement(InboundSettlementManage inboundSettlementManage) {
        inboundSettlementManage.setAuditStatus(1);
        inboundSettlementManage.setWarehouseId("1");
        inboundSettlementManage.setAccountPeriod("");
        inboundSettlementManage.setReceiveName(ThreadLocalUtil.getCurrentUser().getUserName());
        inboundSettlementManage.setReceivePhone(ThreadLocalUtil.getCurrentUser().getUserMobile());
        inboundSettlementManage.setGmtCreate(new Date());
        save(inboundSettlementManage);
    }

    @Override
    public void updateSettlement(InboundSettlementManage inboundSettlementManage) {
        updateById(inboundSettlementManage);
    }

    @Override
    public PageUtils<InboundSettlementManage> queryPage(JSONObject jsonObject, LambdaQueryWrapper<InboundSettlementManage> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String contractNo = (String) innerMap.get("contractNo");
        String supplierName = (String) innerMap.get("supplierName");
        Integer inboundType = (Integer) innerMap.get("inboundType");
        Integer auditStatus = (Integer) innerMap.get("auditStatus");
        String startCreateDate = (String) innerMap.get("startCreateDate");
        String endCreateDate = (String) innerMap.get("endCreateDate");
        if (StringUtils.isNotBlank(keywords)) {
            queryWrapper.and((t) -> {
                t.like(InboundSettlementManage::getContractNo, keywords)
                        .or()
                        .like(InboundSettlementManage::getSupplierName, keywords);

            });
        }
        if (!StringUtils.isEmpty(contractNo)) {
            queryWrapper.like(InboundSettlementManage::getContractNo, contractNo);
        }
        if ( null != inboundType) {
            queryWrapper.eq(InboundSettlementManage::getInboundType, inboundType);
        }
        if ( null != auditStatus) {
            queryWrapper.eq(InboundSettlementManage::getAuditStatus, auditStatus);
        }
        if (!StringUtils.isEmpty(supplierName)) {
            queryWrapper.like(InboundSettlementManage::getSupplierName, supplierName);
        }
        queryWrapper.between(StringUtils.isNotBlank(startCreateDate) && StringUtils.isNotEmpty(endCreateDate), InboundSettlementManage::getGmtCreate, startCreateDate, endCreateDate);
        IPage<InboundSettlementManage> page = this.page(
                new Query<InboundSettlementManage>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils<InboundSettlementManage>(page);
    }

    @Override
    public void export(JSONObject jsonObject, HttpServletResponse response) {
        LambdaQueryWrapper<InboundSettlementManage> queryWrapper = new LambdaQueryWrapper<>();
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String contractNo = (String) innerMap.get("contractNo");
        String supplierName = (String) innerMap.get("supplierName");
        Integer inboundType = (Integer) innerMap.get("inboundType");
        Integer auditStatus = (Integer) innerMap.get("auditStatus");
        String startCreateDate = (String) innerMap.get("startCreateDate");
        String endCreateDate = (String) innerMap.get("endCreateDate");
        if (StringUtils.isNotBlank(keywords)) {
            queryWrapper.and((t) -> {
                t.like(InboundSettlementManage::getContractNo, keywords)
                        .or()
                        .like(InboundSettlementManage::getSupplierName, keywords);

            });
        }
        if (!StringUtils.isEmpty(contractNo)) {
            queryWrapper.like(InboundSettlementManage::getContractNo, contractNo);
        }
        if ( null != inboundType) {
            queryWrapper.eq(InboundSettlementManage::getInboundType, inboundType);
        }
        if ( null != auditStatus) {
            queryWrapper.eq(InboundSettlementManage::getAuditStatus, auditStatus);
        }
        if (!StringUtils.isEmpty(supplierName)) {
            queryWrapper.like(InboundSettlementManage::getSupplierName, supplierName);
        }
        queryWrapper.between(StringUtils.isNotBlank(startCreateDate) && StringUtils.isNotEmpty(endCreateDate), InboundSettlementManage::getGmtCreate, startCreateDate, endCreateDate);
        List<InboundSettlementManage> list = inboundSettlementManageMapper.selectList(queryWrapper);
        try {
            List<InboundSettlementManageVO> dataMap =list.stream().map(item->{
                  InboundSettlementManageVO vo = new InboundSettlementManageVO();
                  vo.setSupplierType( 0 == item.getSupplierType() ? "零星采购" : 1 == item.getSupplierType() ? "大宗临购":"周转材料");
                  vo.setSupplierName(item.getSupplierName());
                  vo.setContractNo(item.getContractNo());
                  vo.setInvoiceNum(item.getInvoiceNum());
                  vo.setInboundType(item.getInboundType() == 1 ? "手动入库" : "自动入库");
                  vo.setWarehouseId(item.getWarehouseId());
                  vo.setRateAmount(item.getRateAmount());
                  vo.setNoRateAmount(item.getNoRateAmount());
                  vo.setNum(item.getNum());
                  vo.setReceiveName(item.getReceiveName());
                  vo.setReceivePhone(item.getReceivePhone());
                  return vo;
            }).collect(Collectors.toList());
            EasyExcelUtils.writeWeb("入库结算单",InboundSettlementManageVO.class, dataMap, "入库结算单", response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void updateState(String id, int state) {
        InboundSettlementManage manage = new InboundSettlementManage();
        manage.setId(id);
        manage.setAuditStatus(state);
        updateById(manage);
    }
}
