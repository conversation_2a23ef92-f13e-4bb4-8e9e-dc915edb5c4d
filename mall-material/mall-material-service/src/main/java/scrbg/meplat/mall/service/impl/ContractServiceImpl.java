package scrbg.meplat.mall.service.impl;

import org.checkerframework.checker.units.qual.A;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.Contract;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.PlatformYearFeeRecord;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.ContractMapper;
import scrbg.meplat.mall.service.ContractService;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.FileService;
import scrbg.meplat.mall.service.PlatformYearFeeRecordService;
import scrbg.meplat.mall.vo.payment.UnUsedContractDto;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
/**
 * @描述：合同 服务类
 * @作者: ye
 * @日期: 2025-03-04
 */
@Service
public class ContractServiceImpl extends ServiceImpl<ContractMapper, Contract> implements ContractService{
    private Logger logger = org.slf4j.LoggerFactory.getLogger(getClass());
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private MallConfig mallConfig;

    @Autowired
    private FileService fileService;
    @Autowired
    private EnterpriseInfoService enterpriseInfoService;


    @Autowired
    private PlatformYearFeeRecordService platformYearFeeRecordService;
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Contract> queryWrapper) {
        IPage<Contract> page = this.page(
        new Query<Contract>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void create(Contract contract) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(contract);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void update(Contract contract) {
        super.updateById(contract);
    }


    @Override
    public Contract getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     @Transactional(rollbackFor = Exception.class)
     @NotResubmit
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
         }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Contract createContractNo(String enterpriseId) {
        String contractNo = null;
        String year = new SimpleDateFormat("yyyy").format(new Date());
        Integer contractYearIndex = null;
        // 获取当年的合同编号
        Contract oContract = lambdaQuery().eq(Contract::getContractYear, year).eq(Contract::getType,1).orderByDesc(Contract::getContractYearIndex).last("limit 1").one();
        if (oContract == null) {
            contractYearIndex = 1;
            contractNo = String.format("SRBG-WZGSSG-RZPT-%s%04d", year, 1);
        } else {
            //  优先获取冗余的合同编号
            List<UnUsedContractDto> contractNoList = super.baseMapper.getContractNoList(year,1,0);
            if(!CollectionUtils.isEmpty(contractNoList)){
                // 获取合同编号的年份后面就是合同编号序号
                UnUsedContractDto cNo= null;
                for (UnUsedContractDto unUsedContractDto : contractNoList) {
                    // 查询缴费表中是否存在该合同编号
                    Integer count = platformYearFeeRecordService.lambdaQuery().eq(PlatformYearFeeRecord::getContractNo , unUsedContractDto.getContractNo()).count();
                    if (count > 0){
                        continue;
                    }else {
                        // 没有使用这个编号则使用这个编号
                        cNo = unUsedContractDto;
                        break;
                    }
                }
                if (cNo != null) {
                    String contractNo1 = cNo.getContractNo();
                    String index = contractNo1.split("-")[3];
                    // index  去掉当年的年份
                    year = index.substring(0,4);
                    // 根据合同编号年份确认序号（使用系统年来确认序号会出现系统年的序号异常）
                    contractYearIndex = Integer.valueOf(index.replace(year,""));
                    //index = index.replace(year,"");
                    //contractYearIndex = Integer.valueOf(index);
                    contractNo = contractNo1;
                    // 需要删除原来的合同编号
                    super.baseMapper.deleteRealByContractNo(contractNo1);
                    logger.warn("原来合同编号数据:{}",cNo);
                    logger.warn("合同编号冗余数据删除：{},新的企业ID:{}",cNo,enterpriseId);
                }else {
                    // 如果没有冗余的合同编号则使用最新的
                    contractYearIndex = oContract.getContractYearIndex() + 1;
                    contractNo = String.format("SRBG-ZBGSSG-RZPT-%s%04d", year, contractYearIndex);
                }
            }else {
                contractYearIndex = oContract.getContractYearIndex() + 1;
                contractNo = String.format("SRBG-ZBGSSG-RZPT-%s%04d", year, contractYearIndex);
            }
        }
        // 创建 一个占位合同
        EnterpriseInfo bEnterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, enterpriseId).one();
        Contract contract = new Contract();
        contract.setContractNo(contractNo);
        contract.setType(1);
        contract.setContractYear(Integer.valueOf(year));
        contract.setName(bEnterpriseInfo.getEnterpriseName()+"占位合同");
        contract.setContractYearIndex(contractYearIndex);
        contract.setPartyAOrgId("占位合同");
        contract.setPartyAOrgName("占位合同");
        contract.setPartyBOrgId(enterpriseId);
        contract.setPartyBOrgName(bEnterpriseInfo.getEnterpriseName());
        contract.setContractJson("占位合同");
        boolean save = save(contract);
        if (!save) throw new BusinessException("合同创建失败！");
        return contract;
    }
}
