package scrbg.meplat.mall.pcwp.third;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import scrbg.meplat.mall.pcwp.FeignConfig;
import scrbg.meplat.mall.pcwp.KeyedPayload;
import scrbg.meplat.mall.pcwp.PcwpClient;
import scrbg.meplat.mall.pcwp.PcwpPageRes;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.third.model.RevolPlan;
import scrbg.meplat.mall.pcwp.third.model.RevolPlanPageQueryCondition;
import scrbg.meplat.mall.pcwp.third.model.RevolPlanPageQueryResult;
import scrbg.meplat.mall.pcwp.third.model.UpdatePlanDtl;
/**
 * 第三方接口服务
 * 周转材料相关
 * 还未对接，这里暂时模拟一下
 */
@FeignClient(name = "pcwp-thirdapi-revol-retail-service", url = "${mall.prodPcwp2Url02}", configuration=FeignConfig.class)
@Profile("!mock-pcwp")
public interface PcwpThirdApiRevolRetailClient extends PcwpClient{
    /**
     * 保存推送的周转材料计划(提供给物资采购平台)
     * @param revolRetailPlan
     * @return
     */
    @PostMapping("/thirdapi/todo")
    PcwpRes<String> saveRevolRetailPlan(@RequestBody KeyedPayload<RevolPlan> revolRetailPlan,
                                                @RequestHeader("token") String token, 
                                                @RequestHeader("syscode") String syscode); 
    
    /**
     * 反写周转材料计划商城订单数量
     *
     * @param payload
     * @param token
     * @param sysCode
     * @return 
     */
    @PostMapping("todo")
    PcwpRes<Void> updatePlanDtl(
            @RequestBody KeyedPayload<List<UpdatePlanDtl>> payload,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );
    
    /**
     * 分页查询周转材料计划
     *
     * @param filter
     * @param token
     * @param sysCode
     * @return
     */
    @PostMapping("todo")
    PcwpRes<PcwpPageRes<RevolPlanPageQueryResult>> queryPageRevolPlan(
            @RequestBody RevolPlanPageQueryCondition filter,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );
}
