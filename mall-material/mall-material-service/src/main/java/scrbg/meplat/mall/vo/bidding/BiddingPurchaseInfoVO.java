package scrbg.meplat.mall.vo.bidding;

import lombok.Data;
import scrbg.meplat.mall.entity.*;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-07-20 16:37
 */
@Data
public class BiddingPurchaseInfoVO extends BiddingPurchase {

    /**
     * 竞价明细
     */
    private List<BiddingProduct> biddingProducts;

    /**
     * 审核记录
     */
    private List<AuditRecord> auditRecords;

    /**
     * 报价记录
     */
    private List<BiddingBidRecord> biddingBidRecords;

    /**
     * 供应商参与
     */
    private List<BiddingSuppliers> biddingSuppliers;
    /**
     * 邀请供应商列表
     */
    private List<BiddingInvitationRelevance> biddingInviteSuppliers;
}
