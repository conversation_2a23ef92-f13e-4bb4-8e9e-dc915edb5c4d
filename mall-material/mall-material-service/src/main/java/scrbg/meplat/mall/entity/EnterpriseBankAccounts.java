package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
import scrbg.meplat.mall.entity.parent.MustBaseTwoEntity;
/**
 * @描述：企业-对公账户表
 * @作者: ye
 * @日期: 2025-03-06
 */
/**
 * 企业-对公账户表
 */
@ApiModel(value = "企业-对公账户表")
@Data
@TableName("enterprise_bank_accounts")
public class EnterpriseBankAccounts extends MustBaseTwoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "企业id")
    private String enterpriseBankAccountsId;

    /**
     * 企业唯一标识
     */
    @ApiModelProperty(value = "企业唯一标识")
    private String enterpriseId;

    /**
     * 开户银行
     */
    @ApiModelProperty(value = "开户银行")
    private String bankName;

    /**
     * 银行账户名称
     */
    @ApiModelProperty(value = "银行账户名称")
    private String accountName;

    /**
     * 银行账号（加密存储）
     */
    @ApiModelProperty(value = "银行账号（加密存储）")
    private String accountNumber;

    /**
     * 开票备注
     */
    @ApiModelProperty(value = "开票备注")
    private String invoiceNote;

    /**
     * 账户状态（1启用，0禁用）
     */
    @ApiModelProperty(value = "账户状态")
    private Boolean status;

    /**
     * 逻辑删除 -1: 删除 0:未删除
     */
    @ApiModelProperty(value = "逻辑删除 -1: 删除 0:未删除")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date gmtModified;

    /**
     * 创建人Id
     */
    @ApiModelProperty(value = "创建人Id")
    private String founderId;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
    private String founderName;



    /**
     * 修改人名称
     */
    @ApiModelProperty(value = "修改人名称")
    private String modifyName;

    /**
     * 修改人id
     */
    @ApiModelProperty(value = "修改人id")
    private String modifyId;



    /**
     * 法定身份证人像面(记录id)
     */
    @ApiModelProperty(value = "法定身份证人像面(记录id)")
    private String legalCardPortraitFaceId;

    /**
     * 法定身份证人像面(存地址)
     */
    @ApiModelProperty(value = "法定身份证人像面(存地址)")
    private String legalCardPortraitFace;

    /**
     * 法定身份证国徽面（记录id）
     */
    @ApiModelProperty(value = "法定身份证国徽面（记录id）")
    private String legalCardPortraitNationalEmblemId;

    /**
     * 法定身份证国徽面（存地址）
     */
    @ApiModelProperty(value = "法定身份证国徽面（存地址）")
    private String legalCardPortraitNationalEmblem;

    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    private String legalRepresentative;

    /**
     * 法定代表人姓名
     */
    @ApiModelProperty(value = "法定代表人姓名")
    private String legalName;

    /**
     * 法定代表人身份证号
     */
    @ApiModelProperty(value = "法定代表人身份证号")
    private String legalNumber;

    /**
     * 有效期开始日期
     */
    @ApiModelProperty(value = "有效期开始日期")
    private Date legalStartTime;

    /**
     * 有效期结束日期
     */
    @ApiModelProperty(value = "有效期结束日期")
    private Date legalEndTime;

    /**
     * 是否长期（1长期，0无）
     */
    @ApiModelProperty(value = "是否长期（1长期，0无）")
    private Boolean isPermanent;

    @ApiModelProperty(value = "附件列表")
    @TableField(
            exist = false
    )
    private List<File> files;
}