<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.MaterialReconciliationDtlMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.MaterialReconciliationDtl" id="MaterialReconciliationDtlMap">
        <result property="reconciliationDtlId" column="reconciliation_dtl_id"/>
        <result property="reconciliationId" column="reconciliation_id"/>
        <result property="sourceDtlId" column="source_dtl_id"/>
        <result property="materialClassId" column="material_class_id"/>
        <result property="materialClassName" column="material_class_name"/>
        <result property="materialId" column="material_id"/>
        <result property="materialMallId" column="material_mall_id"/>
        <result property="materialName" column="material_name"/>
        <result property="spec" column="spec"/>
        <result property="unit" column="unit"/>
        <result property="texture" column="texture"/>
        <result property="price" column="price"/>
        <result property="quantity" column="quantity"/>
        <result property="acceptanceAmount" column="acceptance_amount"/>
        <result property="settledAmmout" column="settled_ammout"/>
        <result property="freightPrice" column="freight_price"/>
        <result property="fixationPrice" column="fixation_price"/>
        <result property="state" column="state"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="sort" column="sort"/>
        <result property="remarks" column="remarks"/>
        <result property="isDelete" column="is_delete"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>

    <select id="listLedgerCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM material_reconciliation_dtl mrd
        INNER JOIN material_reconciliation mr ON mrd.reconciliation_id = mr.reconciliation_id
        AND mr.is_delete = 0
        AND mrd.is_delete = 0
        <where>
            <if test="dto.orgId != null ">
                and mr.`purchasing_org_id` = #{dto.orgId}
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and mr.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.productType != null ">
                and mr.`reconciliation_product_type` = #{dto.productType}
            </if>
            <if test="dto.assetType != null ">
                and mr.`reconciliation_product_type` = #{dto.assetType}
            </if>
            <if test="dto.ledgerType != null ">
                and mr.`reconciliation_product_type` = #{dto.ledgerType}
            </if>
            <if test="dto.reconciliationNo != null ">
                and mr.`reconciliation_no` = #{dto.reconciliationNo}
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and mrd.`material_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (
                mrd.`material_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or mr.`supplier_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and mr.`end_time` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and osd.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and osd.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
    </select>
    <select id="ledgerList" resultType="scrbg.meplat.mall.vo.platform.ReconciliationLedgerListVo">
        select * from (
        SELECT mr.purchasing_org_id as purchasingOrgId,
        mr.purchasing_org_name as purchasingOrgName,
        mr.supplier_id as supplierId,
        mr.supplier_name as supplierName,
        mr.reconciliation_product_type as reconciliationProductType,
        mr.reconciliation_product_type as ReconciliationType,
        mr.reconciliation_id as reconciliationId,
        mr.reconciliation_no as reconciliationNo,
        mrd.material_id as materialId,
        mrd.material_name as materialName,
        mrd.spec as spec,
        mrd.unit as unit,
        mrd.quantity as quantity,
        mrd.price as price,
        mrd.no_rate_price as noRatePrice,
        mrd.acceptance_amount as acceptanceAmount,
        mrd.tax_amount as taxAmount,
        mrd.acceptance_no_rate_amount as acceptanceNoRateAmount,
        mr.start_time as startTime,
        mr.end_time as endTime,
        mr.state as state
        FROM material_reconciliation_dtl mrd
        INNER JOIN material_reconciliation mr ON mrd.reconciliation_id = mr.reconciliation_id
        AND mr.is_delete = 0
        AND mrd.is_delete = 0
        <where>
            <if test="dto.orgId != null ">
                and mr.`purchasing_org_id` = #{dto.orgId}
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and mr.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.productType != null ">
                and mr.`reconciliation_product_type` = #{dto.productType}
            </if>
            <if test="dto.assetType != null ">
                and mr.`reconciliation_product_type` = #{dto.assetType}
            </if>
            <if test="dto.ledgerType != null ">
                and mr.`reconciliation_product_type` = #{dto.ledgerType}
            </if>
            <if test="dto.reconciliationNo != null ">
                and mr.`reconciliation_no` = #{dto.reconciliationNo}
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and mrd.`material_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (
                mrd.`material_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or mr.`supplier_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and mr.`end_time` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and osd.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and osd.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        ) b order by b.endTime desc
    </select>
    <select id="selCountAmount" resultType="java.math.BigDecimal">
        select sum(b.totalAmount) from (
        SELECT mrd.acceptance_amount as totalAmount
        FROM material_reconciliation_dtl mrd
        INNER JOIN material_reconciliation mr ON mrd.reconciliation_id = mr.reconciliation_id
        AND mr.is_delete = 0
        AND mrd.is_delete = 0
        <where>
            <if test="dto.orgId != null ">
                and mr.`purchasing_org_id` = #{dto.orgId}
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and mr.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.productType != null ">
                and mr.`reconciliation_product_type` = #{dto.productType}
            </if>
            <if test="dto.assetType != null ">
                and mr.`reconciliation_product_type` = #{dto.assetType}
            </if>
            <if test="dto.ledgerType != null ">
                and mr.`reconciliation_product_type` = #{dto.ledgerType}
            </if>
            <if test="dto.reconciliationNo != null ">
                and mr.`reconciliation_no` = #{dto.reconciliationNo}
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and mrd.`material_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (
                mrd.`material_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or mr.`supplier_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and mr.`end_time` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and osd.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and osd.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        ) b
    </select>
    <select id="selCountNoRateAmount" resultType="java.math.BigDecimal">
        select sum(b.countNoRateAmount) from (
        SELECT mrd.acceptance_no_rate_amount as countNoRateAmount
        FROM material_reconciliation_dtl mrd
        INNER JOIN material_reconciliation mr ON mrd.reconciliation_id = mr.reconciliation_id
        AND mr.is_delete = 0
        AND mrd.is_delete = 0
        <where>
            <if test="dto.orgId != null ">
                and mr.`purchasing_org_id` = #{dto.orgId}
            </if>
            <if test="dto.supplierName != null and dto.supplierName != ''">
                and mr.`supplier_name` LIKE CONCAT('%',#{dto.supplierName},'%')
            </if>
            <if test="dto.productType != null ">
                and mr.`reconciliation_product_type` = #{dto.productType}
            </if>
            <if test="dto.assetType != null ">
                and mr.`reconciliation_product_type` = #{dto.assetType}
            </if>
            <if test="dto.ledgerType != null ">
                and mr.`reconciliation_product_type` = #{dto.ledgerType}
            </if>
            <if test="dto.reconciliationNo != null ">
                and mr.`reconciliation_no` = #{dto.reconciliationNo}
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and mrd.`material_name` LIKE CONCAT('%',#{dto.productName},'%')
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (
                mrd.`material_name` LIKE CONCAT('%',#{dto.keywords},'%')
                or mr.`supplier_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.startFinishDate != null and dto.endFinishDate != '' and dto.startFinishDate != null and dto.endFinishDate != ''">
                and mr.`end_time` between #{dto.startFinishDate} and #{dto.endFinishDate}
            </if>
            <if test="dto.belowPrice != null and dto.belowPrice != ''">
                and osd.`product_price` &lt;= #{dto.belowPrice}
            </if>
            <if test="dto.abovePrice != null and dto.abovePrice != ''">
                and osd.`product_price` &gt;= #{dto.abovePrice}
            </if>
        </where>
        ) b
    </select>

</mapper>