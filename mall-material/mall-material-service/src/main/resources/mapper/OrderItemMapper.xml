<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.OrderItemMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.OrderItem" id="OrderItemMap">
        <result property="orderItemId" column="order_item_id"/>
        <result property="orderId" column="order_id"/>
        <result property="orderSn" column="order_sn"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="productImg" column="product_img"/>
        <result property="skuId" column="sku_id"/>
        <result property="skuName" column="sku_name"/>
        <result property="costPrice" column="cost_price"/>
        <result property="productPrice" column="product_price"/>
        <result property="buyCounts" column="buy_counts"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="isComment" column="is_comment"/>
        <result property="buyTime" column="buy_time"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderName" column="founder_name"/>
        <result property="founderId" column="founder_id"/>
        <result property="remarks" column="remarks"/>
        <result property="productType" column="product_type"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


    <select id="getProductDetailDealRecord" resultType="scrbg.meplat.mall.vo.product.website.ProductDetailDealRecordVO"
            parameterType="Map">
        select o.order_id,o.order_sn,oi.order_item_id, o.user_id, oi.product_name,oi.product_price, oi.buy_counts,
        o.flish_time
        from order_item oi inner join orders o on oi.order_id = o.order_id
        <where>
            oi.is_delete = 0 and o.`order_class` != 3 and
            o.is_delete = 0 and o.state >=6 and oi.mall_type = #{dto.mallType}
            <if test="dto.productId != null and dto.productId != ''">
                and oi.`product_id` = #{dto.productId}
            </if>
            <if test="dto.orderClass != null and dto.orderClass != ''">
                and o.`order_class` != #{orderClass}
            </if>
        </where>
        order by o.flish_time desc
    </select>

    <select id="getProductDetailDealRecordCount" resultType="int"
            parameterType="Map">
        select count(*)
        from order_item oi inner join orders o on oi.order_id = o.order_id
        <where>
            oi.is_delete = 0 and   o.`order_class` != 3 and
            o.is_delete = 0 and o.state = 10 and oi.mall_type = #{dto.mallType}
            <if test="dto.productId != null and dto.productId != ''">
                and oi.`product_id` = #{dto.productId}
            </if>
        </where>
    </select>
    <select id="orderItemListByOrderId" resultType="scrbg.meplat.mall.vo.product.order.OrderItemVo">
        select oi.*, b.name as brand
        from order_item oi
                 LEFT JOIN product p
                           on oi.product_id = p.product_id
                 LEFT JOIN brand b
                           on p.brand_id = b.brand_id
        where ${ew.sqlSegment}

    </select>
    <!--    1。供应商名称  子订单   -->
    <select id="getPlatformOrderItemCount" resultType="scrbg.meplat.mall.entity.OrderItem">
        select oi.*, o.supplier_name,o.shop_name,o.flish_time, o.enterprise_name
        FROM order_item oi
                 left join orders o
                           on oi.order_id = o.parent_order_id
        where ${ew.sqlSegment}


    </select>
    <select id="getPlatformWeekOrderItemCount"
            resultType="scrbg.meplat.mall.vo.platform.PlatformOrdersCountVO">
        SELECT
        DATE_FORMAT( o.flish_time, '%Y-%u' ) AS WEEK,
        count(*) AS count
        FROM order_item oi
        left join orders o
        on oi.order_id = o.parent_order_id
        <where>
             o.state = 10
            <if test="dto.shopId != null">
                and `shop_id` = #{dto.shopId}
            </if>
            <if test="dto.orderClass != null and dto.orderClass !='' ">
                and `order_class` = #{dto.orderClass}
            </if>
            <if test="dto.startDate != null and dto.startDate != '' and dto.endDate != null and dto.endDate != ''">
                and `flish_time` between #{dto.startDate} and #{dto.endDate}
            </if>
        </where>
        GROUP BY
        WEEK
        ORDER BY WEEK


    </select>
    <select id="selectAllTransactionProduct" resultType="scrbg.meplat.mall.vo.platform.TransactionProductVo">
        select oi.order_sn,oi.product_id,oi.order_Item_id,oi.product_sn,oi.product_name,oi.buy_counts,oi.total_amount,o.success_date ,
               DATE_FORMAT(success_date, '%Y-%m-%d %H:%i:%s') AS successDateStr
        from order_item oi
             left join orders  o
             on  oi.order_id=o.order_id
        where ${ew.sqlSegment}
    </select>
    <select id="findAllHistoryOrderItem" resultType="scrbg.meplat.mall.vo.order.CetHistoryOrderItem">
        select oi.* ,o.receiver_address,o.bill_type from order_item oi left join orders o on oi.order_id=o.order_id


            where ${ew.sqlSegment}
    </select>
    <select id="selectProductSoldNumBySn" resultType="java.util.HashMap">
        select oi.product_id as productId, oi.product_sn as productSn, sum(oi.buy_counts) as soldNum from order_item oi
            left join orders o
            on oi.order_id=o.order_id
        where o.order_class !=3 and o.is_delete=0 and oi.product_sn is not null and oi.is_delete=0
        GROUP BY oi.product_sn
    </select>


</mapper>
