<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.MaterialReconciliationMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.MaterialReconciliation" id="MaterialReconciliationMap">
        <result property="reconciliationId" column="reconciliation_id"/>
        <result property="reconciliationNo" column="reconciliation_no"/>
        <result property="title" column="title"/>
        <result property="type" column="type"/>
        <result property="orderId" column="order_id"/>
        <result property="orderSn" column="order_sn"/>
        <result property="businessType" column="business_type"/>
        <result property="contractId" column="contract_id"/>
        <result property="contractSn" column="contract_sn"/>
        <result property="planId" column="plan_id"/>
        <result property="planSn" column="plan_sn"/>
        <result property="sourceBillId" column="source_bill_id"/>
        <result property="sourceBillNo" column="source_bill_no"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierEnterpriseId" column="supplier_enterprise_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="purchaserId" column="purchaser_id"/>
        <result property="purchaserLocalId" column="purchaser_local_id"/>
        <result property="purchaserName" column="purchaser_name"/>
        <result property="purchasingOrgId" column="purchasing_org_id"/>
        <result property="purchasingLocalOrgId" column="purchasing_local_org_id"/>
        <result property="purchasingOrgName" column="purchasing_org_name"/>
        <result property="acceptancerId" column="acceptancer_id"/>
        <result property="acceptancerName" column="acceptancer_name"/>
        <result property="reconciliationAmount" column="reconciliation_amount"/>
        <result property="settleAmount" column="settle_amount"/>
        <result property="stratTime" column="strat_time"/>
        <result property="endTime" column="end_time"/>
        <result property="createType" column="create_type"/>
        <result property="purchaseIsAffirm" column="purchase_is_affirm"/>
        <result property="supplierIsAffirm" column="supplier_is_affirm"/>
        <result property="isPush" column="is_push"/>
        <result property="freight" column="freight"/>
        <result property="taxRate" column="tax_rate"/>
        <result property="nullifyReason" column="nullify_reason"/>
        <result property="nullifyCreatorLocalId" column="nullify_creator_local_id"/>
        <result property="nullifyCreatorId" column="nullify_creator_id"/>
        <result property="nullifyCreator" column="nullify_creator"/>
        <result property="nullifyCreated" column="nullify_created"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="state" column="state"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="sort" column="sort"/>
        <result property="remarks" column="remarks"/>
        <result property="isDelete" column="is_delete"/>
        <result property="mallType" column="mall_type"/>
        <result property="orgName" column="org_name"/>
        <result property="orgId" column="org_id"/>
        <result property="enterpriseId" column="enterprise_id"/>
    </resultMap>


</mapper>