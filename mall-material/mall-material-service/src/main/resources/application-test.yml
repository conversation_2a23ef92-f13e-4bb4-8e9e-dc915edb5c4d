spring:
  rabbitmq:
    tt:
      host: **************
      port: 5672
      username: wzsc                                    # TT待办系统用户名
      password: FABF26F6-7615-EB92-E158-09F3125DC089   # TT待办系统密码
      virtual-host: ToDoList
    sm: #短信
      host: ************** # 主机名
      port: 5672 # 端口
      virtual-host: / # 虚拟主机
      username: mmcp # 用户名
      password: Ms1q2w3e # 密码
    mdm: # 人员和组织数据同步
      host: **************
      port: 5672
      username: wzsc
      password: FABF26F6-7615-EB92-E158-09F3125DC089
      virtual-host: mdm
      exchange: MDM.V2.RYKJK # 交换器名称
      isSync: false
      queues:
        - name: Personinfo_Full_test
          routingKey: Personinfo_Full
        - name: Zcinfo_Full_test
          routingKey: Zcinfo_Full
        - name: Zyzginfo_Full_test
          routingKey: Zyzginfo_Full
        - name: <PERSON>r<PERSON>jinfo_Full_test
          routingKey: Grhjinfo_Full
        - name: <PERSON>z<PERSON>linfo_Full_test
          routingKey: Rz<PERSON>linfo_Full
        - name: Orginfo_Full_test
          routingKey: Orginfo_Full
        - name: Sys_Org_Full_test
          routingKey: Sys_Org_Full
        - name: Sys_ConOrg_Full_test
          routingKey: Sys_ConOrg_Full
        - name: Project_Project_Full_test
          routingKey: Project_Project_Full
        - name: TTAccount_Full_test
          routingKey: TTAccount_Full
  redis:
    host: *************
    port: 6381
    password: 9ImAtEIE
    database: 0
    lettuce:
      pool:
        max-active: 128          # 最大连接数
        max-idle: 32             # 最大空闲连接数
        min-idle: 16             # 最小空闲连接数
        max-wait: 5000ms         # 获取连接的最大等待时间
    timeout: 5000ms              # 连接超时时间
  cache:
    type: redis
    redis:
      #      time-to-live: 3600s # 过期时间
      # key-prefix: CACHE_ # 会导致自己在@Cacheable里设置的名字失效，
      use-key-prefix: true # key值加前缀，不声明默认是使用区域作为前缀
      cache-null-values: true # 缓存控制
  #数据源配置
  datasource:
    username: root
    #    password: root
    password: 9ImAtEIE
    url: jdbc:p6spy:mysql://*************:3308/mall_material_dev?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&useSSL=false&rewriteBatchedStatements=true&allowPublicKeyRetrieval=true
    #    url: *******************************************************************************************************************************************************************
    driver-class-name: com.p6spy.engine.spy.P6SpyDriver

  #云配置
  cloud:
    nacos:
      discovery:
        server-addr: *************:8850
        ip: *************
        username: nacos
        password: 9ImAtEIE
# redission配置，这里直接读取的redis变量.
#redisson:
#  singleserverconfig:
#    address: "redis://${spring.redis.host}:${spring.redis.port}"
#    password: ${spring.redis.password}
#    database: 0
mall:
  prodPcwp2Url: "http://**************:15101"
  prodPcwp2Url02: "http://**************:15103"
  thirdApiToken: "UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA"
  #  isDPlatformAdminOrgId: "1575170370317582336"
  isDPlatformAdminOrgId: "af1f01246cc6-ab00-084c-79e7-76f6022e"
  #  isMPlatformAdminOrgId: "1575170370317582337"
  isMPlatformAdminOrgId: "aa5422473bfb-aeea-9146-8ce7-04ff3c5e"
  pcwp1ContactUrl: "http://***************:7071"
  pcwp1PlanUrl: "http://***************:7073"
  profilesActive: "dev"
  pcwpPurchase: "http://***************:7073/json.rpc"
  isApiImportProductBradDispose: 2 # 1品牌自动新增（品牌是必传）2无品牌不保存，有品牌判断是否存在不存在报错
  templateFormUrl: "/templateForm"
  isShowCode: 1 # 是否显示开发中的代码
  isCountPlanOrderNum: 0 # 是否使用计划数量统计
  isNotRateAmount: 1 # 是否使用累加不含税总金额
  isLoginAuthQuery: 1 # 是否查询登陆权限
  isContractConsumeNum: 1 # 是否使用pcwp记录大宗月供合同数量
  isPlatformFee: 1 # 是否使用平台计费功能
  isZonePrice: 0 # 区域价格开发
  changAmountAndTaxPlanAmount: 0 # //2024年05月15号  应pcwp要求 Amount传不含税，NoTaxPlanAmount也要改成含税TaxPlanAmount
  selectMaterialMonthSupplyPlan: 1 # 是否使用完结计划  1 使用  0不使用
  ossPrefix: "http://*************:9022"
  isPCWPLogin: 1
  isBusinessOrg: 1
  miniProgram: "http://*************:9050/pages/sheet/sheet?isExternal=true&id="
  #  物资子公司自营店数据
  businessShopId: "1878734518961074177"
#  lowValueClass: ""
#  templateFormUrl: "/templateForm"
#  templateFormUrl: "C://aMySoft//templateForm"
#  templateFormUrl: "D://work//templateFormUrl"

seata:
  enabled: false
  application-id: mall-material
  tx-service-group: my_test_tx_group
  enable-auto-data-source-proxy: true
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: *************:8850
      group: SEATA_GROUP
      cluster: default
  #      namespace: eeba1f43-adcf-449f-bc82-754550cce91b
  config:
    type: nacos
    nacos:
      server-addr: *************:8850
      group: SEATA_GROUP
      namespace: 00393e11-71f6-417f-ba84-7afb3bf11a02
  service:
    vgroup-mapping:
      my_test_tx_group: default
    disable-global-transaction: false
  client:
    rm:
      report-success-enable: false
      lock:
        retry-interval: 20 #校验或占用全局锁重试间隔 默认10,单位毫秒
        retry-times: 60 #校验或占用全局锁重试次数 默认30
minio:
  endpoint: http://*************:9004 #Minio服务所在地址
  accessKey: minioadmin #访问的key
  secretKey: 9ImAtEIE #访问的秘钥
knife4j:
  basic:
    enable: true
    username: mall
    password: sj-lf@qjsk1.25

app:
  verify-code: true
  schedule:
    enable: true       #是否开启定时任务