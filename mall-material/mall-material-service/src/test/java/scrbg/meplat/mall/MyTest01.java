package scrbg.meplat.mall;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.ss.usermodel.*;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import scrbg.meplat.mall.entity.ProductCategory;
import scrbg.meplat.mall.mapper.ProductCategoryMapper;
import scrbg.meplat.mall.pcwp.PcwpPageRes;
import scrbg.meplat.mall.pcwp.PcwpService;
import scrbg.meplat.mall.pcwp.third.model.MaterialNew;
import scrbg.meplat.mall.pcwp.third.model.MaterialPageDto;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Log4j2
@SpringBootTest
public class MyTest01 {

    // 常量定义
    private static final int PAGE_SIZE = Integer.MAX_VALUE;
    private static final int PRODUCT_TYPE = 1;
    private static final int PRODUCT_STATE = 1;
    private static final int HANDLE_SUCCESS = 1;
    private static final int HANDLE_FAILED = -1;
    private static final int START_ROW_INDEX = 2;
    //    private static final String FILE_PATH = "E:\\work\\config\\主要材料.xlsx";
    private static final String FILE_PATH = "E:\\work\\缺失物资编码.xlsx";
    private static final String LIKE_CLASS_PATH = "主要材料";

    @Autowired
    private PcwpService pcwpService;
    @Autowired
    private ProductCategoryMapper productCategoryMapper;

    @Test
    public void test(){

        List<String> codeMap = readFirstSheetData(FILE_PATH);
        List<ProductCategory> productCategories = productCategoryMapper.selectList(
                new QueryWrapper<ProductCategory>()
                        .like("class_path", LIKE_CLASS_PATH)
                            .eq("version_id", "1942518269757186050")
        );
        Map<String, MaterialNew> categoryMap = buildCategoryMap(productCategories, codeMap, "1942518269757186050");
        for (String s : codeMap) {
            log.info("构建完成物资映射表，{} =====> {} 条记录", s, categoryMap.get(s));
        }
    }

    private Map<String, MaterialNew> buildCategoryMap(List<ProductCategory> categories,
                                                      List<String> codeMap,
                                                      String versionId) {
        Map<String, MaterialNew> categoryMap = new ConcurrentHashMap<>();
        List<MaterialNew> list = new ArrayList<>();
        if (categories.size() > 0) {
            for (ProductCategory productCategory : categories) {

                //根据分类ID 查询物资详情列表
                MaterialPageDto materialPageDto = new MaterialPageDto();
                materialPageDto.setIsActive(1);
                materialPageDto.setPageIndex(1);
                materialPageDto.setPageSize(Integer.MAX_VALUE);
                materialPageDto.setClassId("a559a794-7c7d-487f-98a8-4980e9e97fc4");
                materialPageDto.setVersionId(versionId);
                PcwpPageRes<MaterialNew> pcwpPageRes = pcwpService.queryPageMaterialDtl(materialPageDto);
                List<MaterialNew> pageResList = pcwpPageRes.getList();
                list.addAll(pageResList);
//                if (pageResList.size() > 0) {
//                    for (MaterialNew material : pageResList) {
//                        if (LIKE_CLASS_PATH .equals("主要材料")){
//                            categoryMap.put(material.getBillNo(),material);
//
//                        }
//
//                    }
//                }

            }
        }
        return categoryMap;

    }



    /**
     * 读取Excel第一个Sheet的数据，从第三行开始，跳过首单元格为空的行
     */
    public List<String> readFirstSheetData(String filePath) {
        List<String> list = new ArrayList<>();

        try (FileInputStream fis = new FileInputStream(new File(filePath));
             Workbook workbook = WorkbookFactory.create(fis)) {

            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                log.warn("Excel文件中没有找到Sheet");
                return list;
            }

            int lastRowNum = sheet.getLastRowNum();
            log.info("开始读取Excel数据，从第 {} 行到第 {} 行",
                    START_ROW_INDEX + 1, lastRowNum + 1);

            for (int rowIndex = START_ROW_INDEX; rowIndex <= lastRowNum; rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    continue;
                }

                // 检查第一个单元格是否为空
                String firstCellValue = getCellValue(row.getCell(0));
                if (firstCellValue == null || firstCellValue.trim().isEmpty()) {
                    log.debug("跳过第 {} 行，因为第一个单元格为空", rowIndex + 1);
                    continue;
                }

                // 获取需要的单元格数据
                String cell1 = getCellValue(row.getCell(1));
//                String cell2 = getCellValue(row.getCell(11));
                list.add( cell1);

            }

        } catch (IOException e) {
            log.error("读取Excel文件失败", e);
            throw new RuntimeException("Excel文件读取错误", e);
        }

        return list;
    }

    /**
     * 根据单元格类型获取单元格的值
     */
    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字类型，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    }
                    return String.valueOf(numericValue);
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return cell.getDateCellValue().toString();
                    } else {
                        return String.valueOf(cell.getNumericCellValue());
                    }
                } catch (IllegalStateException e) {
                    return cell.getStringCellValue().trim();
                }
            default:
                return "";
        }
    }
}
